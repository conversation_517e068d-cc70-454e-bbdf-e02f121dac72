<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <SpaRoot>..\Client</SpaRoot>
    <SpaProxyLaunchCommand>npm run dev</SpaProxyLaunchCommand>
    <SpaProxyServerUrl>http://localhost:3000</SpaProxyServerUrl>
    <InvariantGlobalization>false</InvariantGlobalization>
  </PropertyGroup>

  <ItemGroup>
    <Content Update="appsettings*.json" CopyToPublishDirectory="Never" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Client\**">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AspNetCore.HealthChecks.UI.Client" />
    <PackageReference Include="Microsoft.AspNetCore.SpaProxy" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" />
    <PackageReference Include="Scalar.AspNetCore" />
    <PackageReference Include="Swashbuckle.AspNetCore" />
    <PackageReference Include="MiniProfiler.AspNetCore.Mvc" />
    <PackageReference Include="MiniProfiler.EntityFrameworkCore" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Client\Client.esproj">
      <ReferenceOutputAssembly>true</ReferenceOutputAssembly>
    </ProjectReference>
    <ProjectReference Include="..\Shared\Shared.csproj" />
    <ProjectReference Include="..\Modules\Users\Users.csproj" />
    <ProjectReference Include="..\Modules\Customers\Customers.csproj" />
    <ProjectReference Include="..\Modules\DynamicForms\DynamicForms.csproj" />
    <ProjectReference Include="..\Modules\Conversations\Conversations.csproj" />
    <ProjectReference Include="..\Modules\Requests\Requests.csproj" />
    <ProjectReference Include="..\Modules\General\General.csproj" />
    <ProjectReference Include="..\Modules\Tasks\Tasks.csproj" />
    <ProjectReference Include="..\Modules\Calendar\Calendar.csproj" />
  </ItemGroup>

</Project>