using System.Reflection;
using System.Text.Json.Serialization;
using Conversations;
using Customers;
using DynamicForms;
using General;
using Host.Infrastructure;
using Host.Infrastructure.OData;
using Microsoft.AspNetCore.Authentication.BearerToken;
using Microsoft.AspNetCore.Identity;
using Microsoft.OpenApi.Models;
using Requests;
using Scalar.AspNetCore;
using Shared;
using Shared.Application;
using Shared.Endpoints;
using Shared.Infrastructure.Excel;
using Shared.Infrastructure.Localization;
using Swashbuckle.AspNetCore.SwaggerUI;
using Tasks;
using Users;
using Calendar;
using Calendar.Application.Abstractions;
using Calendar.Application.CalendarNotes.Common;

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddSharedModule(builder.Host, builder.Configuration);
builder.Services.AddScoped<IWorkContext, WorkContext>();
builder.Services.AddScoped<IReminderScheduler, ReminderScheduler>();

builder.Services.AddMemoryCache();
builder.Services.AddAuthentication().AddBearerToken(IdentityConstants.BearerScheme, options =>
{
    options.BearerTokenExpiration = TimeSpan.FromDays(150);
    options.Events = new BearerTokenEvents
    {
        OnMessageReceived = context =>
        {
            var accessToken = context.Request.Query["access_token"];
            var path = context.HttpContext.Request.Path;

            if (!string.IsNullOrEmpty(accessToken) && path.StartsWithSegments("/hubs"))
            {
                context.Token = accessToken;
            }
            return Task.CompletedTask;
        }
    };
});
builder.Services.AddAuthorizationBuilder();
builder.Services.AddMiniProfiler(options => options.ColorScheme = StackExchange.Profiling.ColorScheme.Auto).AddEntityFramework();
builder.Services.AddHealthChecks();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(x =>
{
    x.SwaggerDoc("apiv1", new OpenApiInfo
    {
        Title = "Application Api v1",
        Description = "Application Api v1."
    });
    x.OperationFilter<ODataOperationFilter>();
    x.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = @"JWT Authorization header using the Bearer scheme.
                    Enter 'Bearer' [space] and then your token in the text input below.
                    Example: 'Bearer 12345abcdef'",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });
    x.AddSecurityRequirement(new OpenApiSecurityRequirement()
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                },
                Scheme = "oauth2",
                Name = "Bearer",
                In = ParameterLocation.Header,
            },
            new List<string>()
        }
    });
});
builder.Services.ConfigureHttpJsonOptions(options =>
{
    options.SerializerOptions.PropertyNamingPolicy = null;
    options.SerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
});
builder.Services.AddCors(options => options.AddPolicy("AllowAll", policy => policy.AllowAnyHeader().AllowAnyMethod().AllowAnyOrigin()));
builder.Services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
builder.Services.AddUsersModule(builder.Configuration);
builder.Services.AddCustomersModule(builder.Configuration);
builder.Services.AddDynamicFormsModule(builder.Configuration);
builder.Services.AddConversationsModule(builder.Configuration);
builder.Services.AddRequestsModule(builder.Configuration);
builder.Services.AddGeneralModule(builder.Configuration);
builder.Services.AddTasksModule(builder.Configuration); 
builder.Services.AddCalendarModule(builder.Configuration);

//TODO: ilgili modüle tanımlanmalı
builder.Services.Configure<ExcelImportSettings>(builder.Configuration.GetSection("ExcelImport"));

builder.Services.AddEndpoints(Assembly.GetExecutingAssembly());
builder.Services.AddControllersWithViews().AddJsonOptions(opts => opts.JsonSerializerOptions.PropertyNamingPolicy = null);
builder.Services.AddExceptionHandler<GlobalExceptionHandler>();
builder.Services.AddProblemDetails();

var app = builder.Build();

app.UseHsts();
app.UseHttpsRedirection();
app.UseDefaultFiles();
app.UseStaticFiles();
if (app.Environment.IsDevelopment())
{
    app.UseMiniProfiler();
    app.UseDeveloperExceptionPage();
}
app.UseSwagger(options => options.RouteTemplate = "openapi/{documentName}.json");
app.MapScalarApiReference();
app.UseSwaggerUI(c =>
{
    c.DocumentTitle = "API";
    c.DocExpansion(DocExpansion.None);
    c.DefaultModelRendering(ModelRendering.Example);
    c.EnableTryItOutByDefault();
    c.DefaultModelExpandDepth(1);
    c.ConfigObject.AdditionalItems.Add("syntaxHighlight", false);
    c.SwaggerEndpoint($"/openapi/apiv1.json", "Api v1");
});
app.MapHealthChecks("/healthcheck");
app.UseCors("AllowAll");
app.UseExceptionHandler();
app.UseRouting();
app.MapEndpoints();
app.UseAuthentication();
app.UseAuthorization();

app.UseConversationsModule();
app.UseUsersModule();
app.UseRequestsModule();
app.UseTasksModule();
app.UseCalendarModule();

app.MapControllerRoute(
    name: "default",
    pattern: "{controller}/{action=Index}/{id?}");
app.MapFallbackToFile("/index.html");

await app.RunAsync();
