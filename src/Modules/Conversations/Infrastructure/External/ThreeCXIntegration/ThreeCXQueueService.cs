using System.Net.Http.Headers;
using System.Net.Http.Json;
using Conversations.Application.Abstractions;
using Shared.Application;
using Shared.Domain;

namespace Conversations.Infrastructure.External.ThreeCXIntegration;

public class ThreeCXQueueService(
    AppSettings appSettings
) : IThreeCXQueueService
{
    private readonly AppSettings _appSettings = appSettings;

    public async Task<Result<List<QueueDto>>> GetAllQueues()
    {
        try
        {
            var tokenResult = await GetAuthTokenAsync();
            if (!tokenResult.IsSuccess)
            {
                return Result.Failure<List<QueueDto>>(tokenResult.Error);
            }
            var apiUrl = _appSettings.ThreeCXApiUrl;
            using var client = new HttpClient();
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", tokenResult.Value);

            var response = await client.GetAsync($"{apiUrl}/api/Control/GetAllQueues");
            if (!response.IsSuccessStatusCode)
            {
                var error = await response.Content.ReadAsStringAsync();
                return Result.Failure<List<QueueDto>>($"Failed to get 3CX queues: {error}");
            }

            var queueResponse = await response.Content.ReadFromJsonAsync<List<QueueResponse>>();
            var queues = queueResponse?.Select(x => new QueueDto(
                x.QueueId,
                x.Number,
                x.Name,
                x.Agents.Select(a => $"{a.Name} {a.Surname} ({a.Number})").ToList()
            )).ToList() ?? new List<QueueDto>();

            return Result.Success(queues);
        }
        catch (Exception ex)
        {
            return Result.Failure<List<QueueDto>>($"Error getting queues: {ex.Message}");
        }
    }

    public async Task<Result<QueueDto>> GetQueue(string queueId)
    {
        try
        {
            var allQueuesResult = await GetAllQueues();
            if (allQueuesResult.IsSuccess)
            {
                var queue = allQueuesResult.Value.FirstOrDefault(q => q.QueueId == queueId);
                if (queue == null)
                {
                    return Result.Failure<QueueDto>("Queue not found");
                }
                return Result.Success(queue);
            }
            else
            {
                return Result.Failure<QueueDto>(allQueuesResult.Error);
            }
        }
        catch (Exception ex)
        {
            return Result.Failure<QueueDto>($"Error getting queue: {ex.Message}");
        }
    }

    public async Task<Result<QueueDto>> CreateQueue(string queueName, string queueNumber, List<string> agents)
    {
        try
        {
            var tokenResult = await GetAuthTokenAsync();
            if (!tokenResult.IsSuccess)
            {
                return Result.Failure<QueueDto>(tokenResult.Error);
            }
            var apiUrl = _appSettings.ThreeCXApiUrl;
            using var client = new HttpClient();
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", tokenResult.Value);
            var requestData = new
            {
                name = queueName,
                number = queueNumber,
                pollingStrategy = "",
                strategy = "",
                members = agents
            };
            var response = await client.PostAsJsonAsync($"{apiUrl}/api/Queue/create", requestData);
            if (!response.IsSuccessStatusCode)
            {
                var error = await response.Content.ReadAsStringAsync();
                return Result.Failure<QueueDto>($"Failed to create 3CX queue: {error}");
            }
            var createdQueue = new QueueDto("", queueNumber, queueName, agents);
            return Result.Success(createdQueue);
        }
        catch (Exception ex)
        {
            return Result.Failure<QueueDto>($"Error creating queue: {ex.Message}");
        }
    }

    public async Task<Result<QueueDto>> UpdateQueue(string queueId, string queueNumber, string queueName, List<string> agents)
    {
        try
        {
            var tokenResult = await GetAuthTokenAsync();
            if (!tokenResult.IsSuccess)
            {
                return Result.Failure<QueueDto>(tokenResult.Error);
            }
            var apiUrl = _appSettings.ThreeCXApiUrl;
            using var client = new HttpClient();
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", tokenResult.Value);
            var requestData = new
            {
                queueId,
                name = queueName,
                number = queueNumber,
                agents
            };
            var response = await client.PutAsJsonAsync($"{apiUrl}/api/Queue/Update/{queueId}", requestData);
            if (!response.IsSuccessStatusCode)
            {
                var error = await response.Content.ReadAsStringAsync();
                return Result.Failure<QueueDto>($"Failed to update 3CX queue: {error}");
            }
            var updatedQueue = new QueueDto(queueId, queueNumber, queueName, agents);
            return Result.Success(updatedQueue);
        }
        catch (Exception ex)
        {
            return Result.Failure<QueueDto>($"Error updating queue: {ex.Message}");
        }
    }

    public async Task<Result> DeleteQueue(string queueId)
    {
        try
        {
            var tokenResult = await GetAuthTokenAsync();
            if (!tokenResult.IsSuccess)
            {
                return Result.Failure(tokenResult.Error);
            }
            var apiUrl = _appSettings.ThreeCXApiUrl;
            using var client = new HttpClient();
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", tokenResult.Value);
            var response = await client.DeleteAsync($"{apiUrl}/api/Queue/Delete/{queueId}");
            if (!response.IsSuccessStatusCode)
            {
                var error = await response.Content.ReadAsStringAsync();
                return Result.Failure($"Failed to delete 3CX queue: {error}");
            }
            return Result.Success();
        }
        catch (Exception ex)
        {
            return Result.Failure($"Error deleting queue: {ex.Message}");
        }
    }

    private async Task<Result<string>> GetAuthTokenAsync()
    {
        try
        {
            var apiUrl = _appSettings.ThreeCXApiUrl;
            var username = _appSettings.ThreeCXApiUsername;
            var password = _appSettings.ThreeCXApiPassword;
            using var client = new HttpClient();
            var loginData = new { username, password };
            var response = await client.PostAsJsonAsync($"{apiUrl}/api/auth/login", loginData);
            if (!response.IsSuccessStatusCode)
            {
                var error = await response.Content.ReadAsStringAsync();
                return Result.Failure<string>($"Failed to authenticate with 3CX: {error}");
            }
            var tokenResponse = await response.Content.ReadFromJsonAsync<TokenResponse>();
            return Result.Success(tokenResponse?.Token ?? string.Empty);
        }
        catch (Exception ex)
        {
            return Result.Failure<string>($"Error getting auth token: {ex.Message}");
        }
    }

    private class QueueResponse
    {
        public string QueueId { get; set; } = null!;
        public string Name { get; set; } = null!;
        public string Number { get; set; } = null!;
        public List<QueueAgent> Agents { get; set; } = null!;
    }

    private class QueueAgent
    {
        public string Name { get; set; } = null!;
        public string Surname { get; set; } = null!;
        public string Number { get; set; } = null!;
    }

    private class TokenResponse
    {
        public string Token { get; set; } = null!;
    }
}
