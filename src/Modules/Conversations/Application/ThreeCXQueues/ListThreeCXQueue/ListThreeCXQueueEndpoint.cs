using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Conversations.Application.ThreeCXQueues.ListThreeCXQueue;

internal sealed class ListThreeCXQueueEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/v1/conversations/threecx-queues", async (
            ISender sender,
            CancellationToken cancellationToken) =>
        {
            var query = new ListThreeCXQueueQuery();
            var result = await sender.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("Conversations.ThreeCXQueues")
        .WithGroupName("apiv1")
        .RequireAuthorization("Conversations.ThreeCXQueues");
    }
}
