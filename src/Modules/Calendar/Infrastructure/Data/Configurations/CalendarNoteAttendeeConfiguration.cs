using Calendar.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Calendar.Infrastructure.Data.Configurations;
public class CalendarNoteAttendeeConfiguration : IEntityTypeConfiguration<CalendarNoteAttendee>
{
    public void Configure(EntityTypeBuilder<CalendarNoteAttendee> builder)
    {
        builder.ToTable("CalendarNoteAttendee", "Tasks");

        builder.HasKey(x => x.Id);

        builder.Property(x => x.UserId)
            .IsRequired();

        builder.Property(x => x.IsOrganizer)
            .IsRequired();

        builder.Property(x => x.Status)
            .IsRequired();
    }
}
