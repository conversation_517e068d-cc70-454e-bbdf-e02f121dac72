using Calendar.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Calendar.Infrastructure.Data.Configurations;
public class CalendarReminderConfiguration : IEntityTypeConfiguration<CalendarReminder>
{
    public void Configure(EntityTypeBuilder<CalendarReminder> builder)
    {
        builder.ToTable("CalendarReminder", "Tasks");

        builder.HasKey(x => x.Id);

        builder.Property(x => x.MinutesBefore)
            .IsRequired();

        builder.Property(x => x.Channel)
            .IsRequired();

        builder.Property(x => x.IsSent)
            .IsRequired();

        builder.Property(x => x.SentAt)
            .IsRequired(false);
    }
}
