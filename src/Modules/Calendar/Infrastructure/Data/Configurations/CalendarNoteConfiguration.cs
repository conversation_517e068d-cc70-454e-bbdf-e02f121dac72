
using Calendar.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Calendar.Infrastructure.Data.Configurations;
public class CalendarNoteConfiguration : IEntityTypeConfiguration<CalendarNote>
{
    public void Configure(EntityTypeBuilder<CalendarNote> builder)
    {
        builder.ToTable("CalendarNote", "Tasks");

        builder.Has<PERSON>ey(x => x.Id);

        builder.Property(x => x.Title)
            .IsRequired()
            .HasMaxLength(500);

        builder.Property(x => x.Description)
            .HasMaxLength(2000);

        builder.Property(x => x.StartDate)
            .IsRequired();

        builder.Property(x => x.Type)
            .IsRequired();

        builder.Property(x => x.Visibility)
            .IsRequired();

        builder.HasMany(x => x.Reminders)
            .WithOne()
            .HasForeignKey("CalendarNoteId")
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(x => x.Attendees)
            .WithOne()
            .HasForeignKey("CalendarNoteId")
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(x => x.RecurrenceRule)
            .WithOne()
            .HasForeignKey<RecurrenceRule>("CalendarNoteId")
            .OnDelete(DeleteBehavior.Cascade);
    }
}
