using Calendar.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Calendar.Infrastructure.Data.Configurations;
public class CalendarNoteFileConfiguration : IEntityTypeConfiguration<CalendarNoteFile>
{
    public void Configure(EntityTypeBuilder<CalendarNoteFile> builder)
    {
        builder.ToTable("CalendarNoteFile", "Tasks");

        builder.HasKey(x => x.Id);

        builder.Property(x => x.FileName)
            .IsRequired()
            .HasMaxLength(300);

        builder.Property(x => x.FileUrl)
            .IsRequired()
            .HasMaxLength(1000);
    }
}
