using Calendar.Domain;
using MediatR;
using Shared.Application;

namespace Calendar.Application.CalendarNotes.Create;

public class CreateCalendarNoteCommand : IRequest<Result<CreateCalendarNoteResponse>>
{
    public string Title { get; set; }
    public string Description { get; set; }

    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }

    public CalendarNoteType Type { get; set; }
    public CalendarVisibility Visibility { get; set; }

    public Guid? RelatedCustomerId { get; set; }
    public Guid? AssignedUserId { get; set; }
    public Guid? DepartmentId { get; set; }

    public bool IsRecurring { get; set; }
    public RecurrenceRuleDto? Recurrence { get; set; }

    public List<ReminderDto> Reminders { get; set; } = new();

    public List<Guid>? AttendeeUserIds { get; set; }
}
