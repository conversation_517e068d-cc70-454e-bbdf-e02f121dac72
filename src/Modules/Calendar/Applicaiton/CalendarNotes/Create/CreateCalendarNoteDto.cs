

using Calendar.Domain;

namespace Calendar.Application.CalendarNotes.Create;
public class ReminderDto
{
    public int MinutesBefore { get; set; }
    public ReminderChannel Channel { get; set; }
}

public class RecurrenceRuleDto
{
    public RecurrenceType Type { get; set; }
    public int Interval { get; set; }
    public int? OccurrenceCount { get; set; }
    public DateTime? EndDate { get; set; }
}
