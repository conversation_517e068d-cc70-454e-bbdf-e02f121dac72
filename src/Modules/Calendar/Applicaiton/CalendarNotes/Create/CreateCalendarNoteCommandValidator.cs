using Calendar.Domain;
using FluentValidation;
using Shared.Infrastructure.Localization;

namespace Calendar.Application.CalendarNotes.Create;
public class CreateCalendarNoteCommandValidator : AbstractValidator<CreateCalendarNoteCommand>
{
    private readonly ILocalizer _localizer; 
    public CreateCalendarNoteCommandValidator(ILocalizer localizer)
    {
       _localizer = localizer;

        RuleFor(x => x.Title)
            .NotEmpty()
            .MaximumLength(500);

        RuleFor(x => x.StartDate)
            .GreaterThanOrEqualTo(DateTime.UtcNow).WithMessage(_localizer.Get("Calendar.StartTimeWasInThePast"));

        RuleFor(x => x)
            .Must(x => !x.EndDate.HasValue || x.EndDate >= x.StartDate)
            .WithMessage(_localizer.Get("Calendar.EndTimeWasBeforeStartTime"));

        RuleFor(x => x.Type)
            .IsInEnum().WithMessage(_localizer.Get("Calendar.InvalidNoteType"));

        RuleFor(x => x.Visibility)
            .IsInEnum().WithMessage(_localizer.Get("Calendar.InvalidVisibility"));

        When(x => x.Type == CalendarNoteType.CallAuto, () =>
        {
            RuleFor(x => x.RelatedCustomerId).NotNull().WithMessage(_localizer.Get("Calendar.RequiredCustomer"));
        });
    }
}
