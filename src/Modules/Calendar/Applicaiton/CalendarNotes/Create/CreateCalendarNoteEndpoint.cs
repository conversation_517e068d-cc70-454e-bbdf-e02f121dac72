using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Calendar.Application.CalendarNotes.Create;
internal sealed class CreateCalendarNoteEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v1/calendar/notes", async (
            CreateCalendarNoteCommand command,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var result = await mediator.Send(command, cancellationToken);

            return Results.Created($"/api/v1/calendar/notes/{result}", result);
        })
        .WithTags("Calendar.Notes")
        .WithGroupName("apiv1")
        .RequireAuthorization("Calendar.Management");
    }
}
