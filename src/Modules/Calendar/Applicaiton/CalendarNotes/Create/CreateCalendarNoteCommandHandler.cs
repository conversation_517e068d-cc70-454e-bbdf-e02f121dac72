using MediatR;
using Microsoft.Extensions.Logging; 
using Shared.Infrastructure.Localization;
using Calendar.Application.CalendarNotes.Common;
using Shared.Contracts;
using Microsoft.AspNetCore.Http;
using Shared.Application;
using Shared.Application.EventBus;
using Calendar.Domain;
using Calendar.Application.Abstractions; 

namespace Calendar.Application.CalendarNotes.Create;
public class CreateCalendarNoteCommandHandler :  IRequestHandler<CreateCalendarNoteCommand, Result<CreateCalendarNoteResponse>>
{
    private readonly ILogger<CreateCalendarNoteCommandHandler> _logger;
    private readonly IReminderScheduler _reminderScheduler;
    private readonly ISharedCustomerService _customerService;
    private readonly ILocalizer _localizer;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IWorkContext _workContext;
    private readonly IEventBus _eventBus;
    private readonly ICalendarDbContext _dbContext;

    public CreateCalendarNoteCommandHandler(
        ILogger<CreateCalendarNoteCommandHandler> logger,
        IReminderScheduler reminderScheduler,
        ISharedCustomerService customerService,
        ILocalizer localizer,
        IHttpContextAccessor httpContextAccessor,
        IWorkContext workContext,
        IEventBus eventBus,
        ICalendarDbContext dbContext)
    {
        _logger = logger;
        _reminderScheduler = reminderScheduler;
        _customerService = customerService;
        _localizer = localizer;
        _httpContextAccessor = httpContextAccessor;
        _workContext = workContext;
        _eventBus = eventBus;
        _dbContext = dbContext;
    }

    public async Task<Result<CreateCalendarNoteResponse>> Handle(CreateCalendarNoteCommand request, CancellationToken cancellationToken)
    {
        CreateCalendarNoteResponse response = new();

        var validationErrors = new List<string>();
        var currentUserId = _workContext.UserId;

        if (request.Type == CalendarNoteType.CallAuto && request.RelatedCustomerId == null)
        {
            validationErrors.Add(_localizer.Get("Calendar.Validation.MissingCustomerForAutoCall"));
        }

        if (request.Visibility == CalendarVisibility.Departmental && request.DepartmentId == null)
        {
            validationErrors.Add(_localizer.Get("Calendar.Validation.DepartmentRequired"));
        }

        if (request.StartDate < DateTime.UtcNow)
        {
            validationErrors.Add(_localizer.Get("Calendar.Validation.StartDateCannotBePast"));
        }

        if (request.EndDate.HasValue && request.EndDate < request.StartDate)
        {
            validationErrors.Add(_localizer.Get("Calendar.Validation.EndDateBeforeStart"));
        }

        if (validationErrors.Any())
        {
            var errors = validationErrors.Select(msg => new Error("ValidationError", msg, ErrorType.Validation)).ToArray();
            return Result<CreateCalendarNoteResponse>.Validation(new ValidationError(errors), response);
        }

        // Not Oluşturma
        var note = new CalendarNote
        {
            Id = Guid.NewGuid(),
            Title = request.Title,
            Description = request.Description,
            StartDate = request.StartDate,
            EndDate = request.EndDate,
            Type = request.Type,
            Visibility = request.Visibility,
            RelatedCustomerId = request.RelatedCustomerId,
            AssignedUserId = request.AssignedUserId,
            DepartmentId = request.DepartmentId,
            CreatedByUserId = currentUserId,
            CreatedAt = DateTime.UtcNow,
            IsRecurring = request.IsRecurring,
            Reminders = request.Reminders.Select(r => new CalendarReminder
            {
                Id = Guid.NewGuid(),
                MinutesBefore = r.MinutesBefore,
                Channel = r.Channel,
                IsSent = false
            }).ToList()
        };

        if (request.IsRecurring && request.Recurrence is not null)
        {
            note.RecurrenceRule = new RecurrenceRule
            {
                Id = Guid.NewGuid(),
                Type = request.Recurrence.Type,
                Interval = request.Recurrence.Interval,
                OccurrenceCount = request.Recurrence.OccurrenceCount,
                EndDate = request.Recurrence.EndDate
            };
        }

        if (request.AttendeeUserIds?.Any() == true)
        {
            note.Attendees = request.AttendeeUserIds
                .Select(uid => new CalendarNoteAttendee
                {
                    Id = Guid.NewGuid(),
                    UserId = uid,
                    IsOrganizer = uid == currentUserId,
                    Status = AttendanceStatus.Pending
                }).ToList();
        }

        await _dbContext.CalendarNotes.AddAsync(note, cancellationToken);
        await _dbContext.SaveChangesAsync(cancellationToken);

        // Hatırlatmaları Planla
        foreach (var reminder in note.Reminders)
        {
            var triggerTime = note.StartDate.AddMinutes(-reminder.MinutesBefore);
            if (triggerTime > DateTime.UtcNow)
            {
                await _reminderScheduler.ScheduleReminderAsync(note.Id, reminder.Id, triggerTime, reminder.Channel);
            }
        }

        // Event Bus veya Domain Event Hook
        // await _eventBus.PublishAsync(...);
        // notification veya domain event publish edilebilir

        var ip = _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString();
        var userAgent = _httpContextAccessor.HttpContext?.Request?.Headers["User-Agent"].ToString();

        _logger.LogInformation(
            "Calendar note created by UserId: {UserId}. Title: {Title}, Customer: {CustomerId}, Department: {DepartmentId}, IsRecurring: {IsRecurring}, IP: {IP}, UA: {UserAgent}, Date: {Date}",
            currentUserId,
            note.Title,
            note.RelatedCustomerId,
            note.DepartmentId,
            note.IsRecurring,
            ip,
            userAgent,
            DateTime.Now
        );

            response.TotalRecords = 1;
            response.SuccessfulRecords = 1;
            response.Errors = validationErrors;
            response.Success = !validationErrors.Any();

            return Result.Success(response);
    }
}

