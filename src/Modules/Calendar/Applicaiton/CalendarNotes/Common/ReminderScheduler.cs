
using Calendar.Application.Abstractions;
using Calendar.Domain;
using Microsoft.Extensions.Logging;

namespace Calendar.Application.CalendarNotes.Common;
public class ReminderScheduler : IReminderScheduler
{
    private readonly ILogger<ReminderScheduler> _logger;

    public ReminderScheduler(ILogger<ReminderScheduler> logger)
    {
        _logger = logger;
    }

    public Task ScheduleReminderAsync(Guid noteId, Guid reminderId, DateTime triggerTime, ReminderChannel channel)
    {
        // Şimdilik log
        // Sonra Hangfire/Quartz gibi sistemle entegre edelim
        _logger.LogInformation("Reminder scheduled for Note {NoteId}, Reminder {ReminderId} at {Time} via {Channel}", noteId, reminderId, triggerTime, channel);
        return Task.CompletedTask;
    } 
}
