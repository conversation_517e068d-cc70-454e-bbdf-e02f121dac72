using Shared.Domain;

namespace Calendar.Domain;
public class RecurrenceRule: AuditableEntity
{
    public Guid Id { get; set; }
    public Guid CalendarNoteId { get; set; }

    public RecurrenceType Type { get; set; } // Daily, Weekly, Monthly
    public int Interval { get; set; } // Her 2 günde bir gibi
    public int? OccurrenceCount { get; set; } // Kaç tekrar olacak
    public DateTime? EndDate { get; set; }
}
