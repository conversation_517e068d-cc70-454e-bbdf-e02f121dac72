using Shared.Domain;

namespace Calendar.Domain;
public class CalendarNote: AuditableEntity
{
    public Guid Id { get; set; }
    public string Title { get; set; }  
    public string Description { get; set; }

    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }

    public CalendarNoteType Type { get; set; } // CallAuto, Meeting, Task vs.
    public CalendarVisibility Visibility { get; set; } // Personal, Departmental, Public

    public Guid? CreatedByUserId { get; set; }
    public Guid? AssignedUserId { get; set; }
    public Guid? DepartmentId { get; set; }

    public Guid? RelatedCustomerId { get; set; }
    public Guid? RelatedCompanyId { get; set; }

    public bool IsRecurring { get; set; }
    public RecurrenceRule? RecurrenceRule { get; set; }

    public ICollection<CalendarReminder> Reminders { get; set; } = new List<CalendarReminder>();
    public ICollection<CalendarNoteFile> Files { get; set; } = new List<CalendarNoteFile>();

    public ICollection<CalendarNoteAttendee> Attendees { get; set; } = new List<CalendarNoteAttendee>();

    public bool IsDone { get; set; } // İşaretleme özelliği
    public DateTime CreatedAt { get; set; }
}
