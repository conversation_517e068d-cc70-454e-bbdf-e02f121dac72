﻿using System.Reflection;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.OData;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Shared.Endpoints; 
using Calendar.Application.Abstractions;
using Calendar.Infrastructure.Data; 

namespace Calendar;

public static class ModuleInitializer
{
    public static IServiceCollection AddCalendarModule(this IServiceCollection services, IConfiguration configuration)
    {
        var connectionString = configuration.GetConnectionString("ApplicationConnection");
        var assembly = Assembly.GetExecutingAssembly();
        services.AddDbContext<CalendarDbContext>(options =>
            options.UseSqlServer(connectionString, b =>
            {
                b.MigrationsHistoryTable(HistoryRepository.DefaultTableName, "Calendar");
                b.MigrationsAssembly(assembly);
                b.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery);
            }));
        services.AddScoped<ICalendarDbContext>(provider => provider.GetRequiredService<CalendarDbContext>());
        services.AddEndpoints(assembly);
        services.AddMediatR(config => config.RegisterServicesFromAssembly(assembly));
        services.AddValidatorsFromAssembly(assembly, includeInternalTypes: true);
        services.AddControllers().AddOData(options => options.Select().Filter().OrderBy().Expand().Count().SetMaxTop(100));

        return services;
    }

    public static IApplicationBuilder UseCalendarModule(this IApplicationBuilder app)
    {
        app.UseEndpoints(endpoints =>
        {
            endpoints.MapControllers();
        });
        return app;
    }
}
