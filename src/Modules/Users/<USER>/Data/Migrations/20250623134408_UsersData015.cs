﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace Users.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class UsersData015 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Code",
                schema: "Users",
                table: "Department",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true);

            migrationBuilder.InsertData(
                schema: "Users",
                table: "Role",
                columns: new[] { "Id", "ConcurrencyStamp", "IsAssignable", "IsBase", "Name", "NormalizedName" },
                values: new object[,]
                {
                    { new Guid("32a6f9de-100a-4187-b640-44d0bf20e7f7"), "32a6f9de-100a-4187-b640-44d0bf20e7f7", true, true, "Reporter", "REPORTER" },
                    { new Guid("eec4daf7-f6f3-47f4-aad6-fc123b38643d"), "eec4daf7-f6f3-47f4-aad6-fc123b38643d", true, true, "Advisor", "ADVISOR" }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("32a6f9de-100a-4187-b640-44d0bf20e7f7"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("eec4daf7-f6f3-47f4-aad6-fc123b38643d"));

            migrationBuilder.DropColumn(
                name: "Code",
                schema: "Users",
                table: "Department");
        }
    }
}
