using Customers.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.Customers.GetCustomerList;

internal sealed class GetCustomerListQueryHandler(
    ICustomersDbContext context,
    IWorkContext workContext
) : IRequestHandler<GetCustomerListQuery, PagedResult<CustomerListItemDto>>
{
    private readonly IWorkContext _workContext = workContext;

    public async Task<PagedResult<CustomerListItemDto>> Handle(
        GetCustomerListQuery request,
        CancellationToken cancellationToken)
    {
        var query = context.Customers
            .Where(x => !x.IsDeleted)
            .AsQueryable();

        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            var searchTerm = request.SearchTerm.ToLower();
            query = query.Where(x =>
                x.Name.ToLower().Contains(searchTerm) ||
                x.Surname.ToLower().Contains(searchTerm) ||
                x.Email.ToLower().Contains(searchTerm) ||
                x.Phone.Contains(searchTerm));
        }
        if (_workContext.HasRole("ADVISOR"))
        {
            var user = await _workContext.GetUserAsync();
            query = query.Where(x => x.AdvisorIds.Contains(user.Id));
        }
        if (request.Kind.HasValue)
        {
            query = query.Where(x => x.Kind == request.Kind.Value);
        }
        if (request.Status.HasValue)
        {
            query = query.Where(x => x.Status == request.Status.Value);
        }
        if (request.Type.HasValue)
        {
            query = query.Where(x => x.Type == request.Type.Value);
        }
        if (!string.IsNullOrWhiteSpace(request.Name))
        {
            query = query.Where(x => x.Name.Contains(request.Name));
        }
        if (!string.IsNullOrWhiteSpace(request.Surname))
        {
            query = query.Where(x => x.Surname.Contains(request.Surname));
        }
        if (!string.IsNullOrWhiteSpace(request.Phone))
        {
            query = query.Where(x => x.Phone.Contains(request.Phone));
        }
        if (!string.IsNullOrWhiteSpace(request.Email))
        {
            query = query.Where(x => x.Email.Contains(request.Email));
        }
        if (!string.IsNullOrWhiteSpace(request.Country))
        {
            query = query.Where(x => x.Addresses.Any(y => y.Country.Contains(request.Country)));
        }
        if (!string.IsNullOrWhiteSpace(request.State))
        {
            query = query.Where(x => x.Addresses.Any(y => y.State.Contains(request.State)));
        }
        if (!string.IsNullOrWhiteSpace(request.City))
        {
            query = query.Where(x => x.Addresses.Any(y => y.City.Contains(request.City)));
        }
        if (!string.IsNullOrWhiteSpace(request.IdentificationNumberOrTaxNumber))
        {
            query = query.Where(x => x.IdentificationNumber.Contains(request.IdentificationNumberOrTaxNumber) || x.TaxNumber.Contains(request.IdentificationNumberOrTaxNumber));
        }
        if (request.SectorId.HasValue)
        {
            query = query.Where(x => x.SectorId == request.SectorId.Value);
        }
        if (request.ProfessionId.HasValue)
        {
            query = query.Where(x => x.ProfessionId == request.ProfessionId.Value);
        }
        if (request.CustomerSourceId.HasValue)
        {
            query = query.Where(x => x.CustomerSourceId == request.CustomerSourceId.Value);
        }
        if (request.NotificationWayId.HasValue)
        {
            query = query.Where(x => x.NotificationWayId == request.NotificationWayId.Value);
        }
        if (request.TopCustomerId.HasValue)
        {
            query = query.Where(x => x.TopCustomerId == request.TopCustomerId.Value);
        }
        if (request.AdvisorId.HasValue)
        {
            query = query.Where(c => c.AdvisorIds != null && c.AdvisorIds.Contains(request.AdvisorId.Value));
        }
        if (request.ClassificationIds?.Length > 0)
        {
            query = query.Where(x => x.CustomerClassifications.Any(y => request.ClassificationIds.Contains(y.ClassificationId)));
        }
        var filteredCount = await query.CountAsync(cancellationToken);
        var totalCount = await context.Customers.Where(x => !x.IsDeleted).CountAsync(cancellationToken);
        var items = await query
            .OrderByDescending(x => x.InsertDate)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(x => new CustomerListItemDto(
                x.Id,
                x.Name,
                x.Surname,
                x.Email,
                x.Phone,
                x.PhonePrefix,
                x.TaxOffice,
                x.TaxNumber,
                x.IdentificationNumber,
                x.Country,
                x.MainLanguage,
                x.AvailableLanguage,
                x.Description,
                x.MailBcc,
                x.Type,
                x.Kind,
                x.Status,
                x.CustomerSourceId,
                x.CustomerSource.Name,
                x.SectorId,
                x.Sector.Name,
                x.ProfessionId,
                x.Profession.Name,
                x.CustomerClassifications.Select(x => x.Classification.Name).ToArray(),
                x.AdvisorIds,
                x.InsertDate))
            .ToListAsync(cancellationToken);

        return new PagedResult<CustomerListItemDto>(items)
        {
            PageNumber = request.PageNumber,
            PageSize = request.PageSize,
            FilteredCount = filteredCount,
            Count = totalCount,
        };
    }
}
