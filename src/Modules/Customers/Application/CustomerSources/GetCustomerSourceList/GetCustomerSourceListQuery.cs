using MediatR;
using Shared.Application;

namespace Customers.Application.CustomerSources.GetCustomerSourceList;

public sealed record GetCustomerSourceListQuery(
    int PageNumber = 1,
    int PageSize = 10,
    string? SearchTerm = null,
    string? SortBy = null,
    bool Ascending = true
) : IRequest<PagedResult<CustomerSourceListDto>>;

public sealed record CustomerSourceListDto(
    Guid Id,
    string Name);
