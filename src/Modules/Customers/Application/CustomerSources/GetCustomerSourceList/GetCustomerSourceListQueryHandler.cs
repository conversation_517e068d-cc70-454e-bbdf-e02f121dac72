using Customers.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Customers.Application.CustomerSources.GetCustomerSourceList;

public sealed class GetCustomerSourceListQueryHandler(
    ICustomersDbContext dbContext
) : IRequestHandler<GetCustomerSourceListQuery, PagedResult<CustomerSourceListDto>>
{
    private readonly ICustomersDbContext _dbContext = dbContext;

    public async Task<PagedResult<CustomerSourceListDto>> Handle(
        GetCustomerSourceListQuery request,
        CancellationToken cancellationToken)
    {
        var query = _dbContext.CustomerSource.AsQueryable();

        var totalCount = await _dbContext.CustomerSource.CountAsync(cancellationToken);
        var filteredCount = await query.CountAsync(cancellationToken);

        var classifications = await query
            .OrderBy(p => p.Name)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(p => new CustomerSourceListDto(
                p.Id,
                p.Name))
            .ToListAsync(cancellationToken);

        var result = PagedResult<CustomerSourceListDto>.Success(classifications);
        result.PageNumber = request.PageNumber;
        result.PageSize = request.PageSize;
        result.Count = totalCount;
        result.FilteredCount = filteredCount;

        return result;
    }
}
