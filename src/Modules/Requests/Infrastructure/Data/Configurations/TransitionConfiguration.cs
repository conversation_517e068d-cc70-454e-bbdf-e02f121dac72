using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Requests.Domain;

namespace Requests.Infrastructure.Data.Configurations;

public class TransitionConfiguration : IEntityTypeConfiguration<Transition>
{
    public void Configure(EntityTypeBuilder<Transition> builder)
    {
        builder.ToTable("Transitions");

        builder.<PERSON><PERSON><PERSON>(t => t.Id);

        builder.Property(t => t.Name)
            .IsRequired()
            .HasMaxLength(100);

        builder.HasOne(t => t.FromNode)
            .WithMany(n => n.FromTransitions)
            .HasForeignKey(t => t.FromNodeId)
            .OnDelete(DeleteBehavior.NoAction);

        builder.HasOne(t => t.ToNode)
            .WithMany(n => n.ToTransitions)
            .HasForeignKey(t => t.ToNodeId)
            .OnDelete(DeleteBehavior.NoAction);

        builder.HasIndex(t => new { t.FromNodeId, t.ToNodeId, t.Name })
            .IsUnique();

        // Self-referencing constraint
        builder.HasCheckConstraint("CK_Transition_DifferentNodes", "[FromNodeId] != [ToNodeId]");
    }
}
