using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Requests.Domain;

namespace Requests.Infrastructure.Data.Configurations;

public class FlowConfiguration : IEntityTypeConfiguration<Flow>
{
    public void Configure(EntityTypeBuilder<Flow> builder)
    {
        builder.ToTable("Flows");

        builder.HasKey(f => f.Id);

        builder.Property(f => f.Name)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(f => f.Description)
            .HasMaxLength(500);

        builder.HasMany(f => f.Nodes)
            .WithOne(n => n.Flow)
            .HasForeignKey(n => n.FlowId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasIndex(f => f.Name)
            .IsUnique();
    }
}
