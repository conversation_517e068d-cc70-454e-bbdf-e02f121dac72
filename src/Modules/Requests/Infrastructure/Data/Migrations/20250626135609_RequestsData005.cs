﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Requests.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class RequestsData005 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "FlowId",
                schema: "Requests",
                table: "TicketSubjects",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Description",
                schema: "Requests",
                table: "Nodes",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: false,
                defaultValue: "");

            migrationBuilder.CreateTable(
                name: "TransitionRules",
                schema: "Requests",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    TransitionId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: false),
                    RuleType = table.Column<int>(type: "int", nullable: false),
                    Order = table.Column<int>(type: "int", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    Configuration = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: false, defaultValue: "{}"),
                    InsertDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdateDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    InsertUserId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    UpdateUserId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    History = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TransitionRules", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TransitionRules_Transitions_TransitionId",
                        column: x => x.TransitionId,
                        principalSchema: "Requests",
                        principalTable: "Transitions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "RuleExecutions",
                schema: "Requests",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    TransitionRuleId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    TicketId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ExecutedByUserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ExecutedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Status = table.Column<int>(type: "int", nullable: false),
                    InputData = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: false, defaultValue: ""),
                    OutputData = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: false, defaultValue: ""),
                    ErrorMessage = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: false, defaultValue: ""),
                    ExecutionTimeMs = table.Column<int>(type: "int", nullable: false),
                    InsertDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdateDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    InsertUserId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    UpdateUserId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    History = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RuleExecutions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RuleExecutions_TransitionRules_TransitionRuleId",
                        column: x => x.TransitionRuleId,
                        principalSchema: "Requests",
                        principalTable: "TransitionRules",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_TicketSubjects_FlowId",
                schema: "Requests",
                table: "TicketSubjects",
                column: "FlowId");

            migrationBuilder.CreateIndex(
                name: "IX_Tickets_SubjectId",
                schema: "Requests",
                table: "Tickets",
                column: "SubjectId");

            migrationBuilder.CreateIndex(
                name: "IX_RuleExecutions_ExecutedAt",
                schema: "Requests",
                table: "RuleExecutions",
                column: "ExecutedAt");

            migrationBuilder.CreateIndex(
                name: "IX_RuleExecutions_ExecutedByUserId",
                schema: "Requests",
                table: "RuleExecutions",
                column: "ExecutedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_RuleExecutions_Status",
                schema: "Requests",
                table: "RuleExecutions",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_RuleExecutions_TicketId",
                schema: "Requests",
                table: "RuleExecutions",
                column: "TicketId");

            migrationBuilder.CreateIndex(
                name: "IX_RuleExecutions_TransitionRuleId",
                schema: "Requests",
                table: "RuleExecutions",
                column: "TransitionRuleId");

            migrationBuilder.CreateIndex(
                name: "IX_TransitionRules_IsActive",
                schema: "Requests",
                table: "TransitionRules",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_TransitionRules_TransitionId_Order",
                schema: "Requests",
                table: "TransitionRules",
                columns: new[] { "TransitionId", "Order" },
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_Tickets_TicketSubjects_SubjectId",
                schema: "Requests",
                table: "Tickets",
                column: "SubjectId",
                principalSchema: "Requests",
                principalTable: "TicketSubjects",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_TicketSubjects_Flows_FlowId",
                schema: "Requests",
                table: "TicketSubjects",
                column: "FlowId",
                principalSchema: "Requests",
                principalTable: "Flows",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Tickets_TicketSubjects_SubjectId",
                schema: "Requests",
                table: "Tickets");

            migrationBuilder.DropForeignKey(
                name: "FK_TicketSubjects_Flows_FlowId",
                schema: "Requests",
                table: "TicketSubjects");

            migrationBuilder.DropTable(
                name: "RuleExecutions",
                schema: "Requests");

            migrationBuilder.DropTable(
                name: "TransitionRules",
                schema: "Requests");

            migrationBuilder.DropIndex(
                name: "IX_TicketSubjects_FlowId",
                schema: "Requests",
                table: "TicketSubjects");

            migrationBuilder.DropIndex(
                name: "IX_Tickets_SubjectId",
                schema: "Requests",
                table: "Tickets");

            migrationBuilder.DropColumn(
                name: "FlowId",
                schema: "Requests",
                table: "TicketSubjects");

            migrationBuilder.DropColumn(
                name: "Description",
                schema: "Requests",
                table: "Nodes");
        }
    }
}
