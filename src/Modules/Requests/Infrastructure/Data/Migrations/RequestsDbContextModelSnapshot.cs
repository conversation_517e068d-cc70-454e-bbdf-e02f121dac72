﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Requests.Infrastructure.Data;

#nullable disable

namespace Requests.Infrastructure.Data.Migrations
{
    [DbContext(typeof(RequestsDbContext))]
    partial class RequestsDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("Requests")
                .HasAnnotation("ProductVersion", "9.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Requests.Domain.Flow", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("History")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<DateTime>("InsertDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("InsertUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("UpdateDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdateUserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Flows", "Requests");
                });

            modelBuilder.Entity("Requests.Domain.Node", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<Guid>("FlowId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("History")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<DateTime>("InsertDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("InsertUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("NodeType")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdateDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdateUserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("FlowId", "Name")
                        .IsUnique();

                    b.ToTable("Nodes", "Requests");
                });

            modelBuilder.Entity("Requests.Domain.RuleExecution", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ErrorMessage")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasDefaultValue("");

                    b.Property<DateTime>("ExecutedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("ExecutedByUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ExecutionTimeMs")
                        .HasColumnType("int");

                    b.Property<string>("History")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("InputData")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasDefaultValue("");

                    b.Property<DateTime>("InsertDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("InsertUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("OutputData")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasDefaultValue("");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid>("TicketId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TransitionRuleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdateDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdateUserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ExecutedAt");

                    b.HasIndex("ExecutedByUserId");

                    b.HasIndex("Status");

                    b.HasIndex("TicketId");

                    b.HasIndex("TransitionRuleId");

                    b.ToTable("RuleExecutions", "Requests");
                });

            modelBuilder.Entity("Requests.Domain.Ticket", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("History")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<DateTime>("InsertDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("InsertUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("NotificationWayId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Priority")
                        .HasColumnType("int");

                    b.Property<Guid>("ReporterUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("StatusId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("SubjectId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime?>("UpdateDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdateUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Watchlist")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("SubjectId");

                    b.ToTable("Tickets", "Requests");
                });

            modelBuilder.Entity("Requests.Domain.TicketComment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Comment")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("History")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<DateTime>("InsertDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("InsertUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TicketId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdateDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdateUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("TicketId");

                    b.ToTable("TicketComments", "Requests");
                });

            modelBuilder.Entity("Requests.Domain.TicketDepartment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("DepartmentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("DepartmentName")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<Guid>("TicketId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("TicketId");

                    b.ToTable("TicketDepartments", "Requests");
                });

            modelBuilder.Entity("Requests.Domain.TicketFile", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ContentType")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<string>("History")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<DateTime>("InsertDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("InsertUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TicketId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdateDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdateUserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("TicketId");

                    b.ToTable("TicketFiles", "Requests");
                });

            modelBuilder.Entity("Requests.Domain.TicketSubject", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("FlowId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("History")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<DateTime>("InsertDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("InsertUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("UpdateDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdateUserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("FlowId");

                    b.ToTable("TicketSubjects", "Requests");
                });

            modelBuilder.Entity("Requests.Domain.Transition", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("FromNodeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("History")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<DateTime>("InsertDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("InsertUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid>("ToNodeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdateDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdateUserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ToNodeId");

                    b.HasIndex("FromNodeId", "ToNodeId", "Name")
                        .IsUnique();

                    b.ToTable("Transitions", "Requests", t =>
                        {
                            t.HasCheckConstraint("CK_Transition_DifferentNodes", "[FromNodeId] != [ToNodeId]");
                        });
                });

            modelBuilder.Entity("Requests.Domain.TransitionRule", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Configuration")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasDefaultValue("{}");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("History")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<DateTime>("InsertDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("InsertUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.Property<int>("RuleType")
                        .HasColumnType("int");

                    b.Property<Guid>("TransitionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdateDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdateUserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("IsActive");

                    b.HasIndex("TransitionId", "Order")
                        .IsUnique();

                    b.ToTable("TransitionRules", "Requests");
                });

            modelBuilder.Entity("Requests.Domain.Node", b =>
                {
                    b.HasOne("Requests.Domain.Flow", "Flow")
                        .WithMany("Nodes")
                        .HasForeignKey("FlowId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Flow");
                });

            modelBuilder.Entity("Requests.Domain.RuleExecution", b =>
                {
                    b.HasOne("Requests.Domain.TransitionRule", "TransitionRule")
                        .WithMany("RuleExecutions")
                        .HasForeignKey("TransitionRuleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("TransitionRule");
                });

            modelBuilder.Entity("Requests.Domain.Ticket", b =>
                {
                    b.HasOne("Requests.Domain.TicketSubject", "Subject")
                        .WithMany()
                        .HasForeignKey("SubjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Subject");
                });

            modelBuilder.Entity("Requests.Domain.TicketComment", b =>
                {
                    b.HasOne("Requests.Domain.Ticket", null)
                        .WithMany()
                        .HasForeignKey("TicketId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Requests.Domain.TicketDepartment", b =>
                {
                    b.HasOne("Requests.Domain.Ticket", "Ticket")
                        .WithMany("TicketDepartment")
                        .HasForeignKey("TicketId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Ticket");
                });

            modelBuilder.Entity("Requests.Domain.TicketFile", b =>
                {
                    b.HasOne("Requests.Domain.Ticket", null)
                        .WithMany("TicketFiles")
                        .HasForeignKey("TicketId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Requests.Domain.TicketSubject", b =>
                {
                    b.HasOne("Requests.Domain.Flow", "Flow")
                        .WithMany()
                        .HasForeignKey("FlowId");

                    b.Navigation("Flow");
                });

            modelBuilder.Entity("Requests.Domain.Transition", b =>
                {
                    b.HasOne("Requests.Domain.Node", "FromNode")
                        .WithMany("FromTransitions")
                        .HasForeignKey("FromNodeId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Requests.Domain.Node", "ToNode")
                        .WithMany("ToTransitions")
                        .HasForeignKey("ToNodeId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("FromNode");

                    b.Navigation("ToNode");
                });

            modelBuilder.Entity("Requests.Domain.TransitionRule", b =>
                {
                    b.HasOne("Requests.Domain.Transition", "Transition")
                        .WithMany("Rules")
                        .HasForeignKey("TransitionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Transition");
                });

            modelBuilder.Entity("Requests.Domain.Flow", b =>
                {
                    b.Navigation("Nodes");
                });

            modelBuilder.Entity("Requests.Domain.Node", b =>
                {
                    b.Navigation("FromTransitions");

                    b.Navigation("ToTransitions");
                });

            modelBuilder.Entity("Requests.Domain.Ticket", b =>
                {
                    b.Navigation("TicketDepartment");

                    b.Navigation("TicketFiles");
                });

            modelBuilder.Entity("Requests.Domain.Transition", b =>
                {
                    b.Navigation("Rules");
                });

            modelBuilder.Entity("Requests.Domain.TransitionRule", b =>
                {
                    b.Navigation("RuleExecutions");
                });
#pragma warning restore 612, 618
        }
    }
}
