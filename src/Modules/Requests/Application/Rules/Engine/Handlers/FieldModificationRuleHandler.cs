using Microsoft.Extensions.Logging;
using Requests.Application.Rules.Engine.Configurations;
using Requests.Domain;
using Requests.Domain.Enums;

namespace Requests.Application.Rules.Engine.Handlers;

public class FieldModificationRuleHandler(
    ILogger<FieldModificationRuleHandler> logger) : BaseRuleHandler
{
    public override RuleType RuleType => RuleType.FieldModification;

    public static readonly string FormConfigurationJson = """
    {
      "title": "<PERSON> Ku<PERSON>",
      "description": "Geçiş sırasında ticket alanlarını otomatik olarak değiştirir",
      "sections": [
        {
          "title": "<PERSON>",
          "fields": [
            {
              "name": "Modifications",
              "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
              "type": "array",
              "required": true,
              "description": "Yapılacak alan de<PERSON>şiklikleri",
              "itemSchema": {
                "type": "object",
                "properties": {
                  "FieldName": {
                    "label": "<PERSON>",
                    "type": "select",
                    "required": true,
                    "description": "Değiştirilecek alan",
                    "options": {
                      "items": [
                        { "value": "Title", "label": "Başlık" },
                        { "value": "Description", "label": "Açıklama" },
                        { "value": "Priority", "label": "Öncelik" },
                        { "value": "Status", "label": "Durum" },
                        { "value": "AssignedUserId", "label": "Atanan Kullanıcı" },
                        { "value": "AssignedDepartmentId", "label": "Atanan Departman" },
                        { "value": "DueDate", "label": "Bitiş Tarihi" },
                        { "value": "EstimatedHours", "label": "Tahmini Saat" },
                        { "value": "ActualHours", "label": "Gerçek Saat" },
                        { "value": "Tags", "label": "Etiketler" },
                        { "value": "CustomField1", "label": "Özel Alan 1" },
                        { "value": "CustomField2", "label": "Özel Alan 2" },
                        { "value": "CustomField3", "label": "Özel Alan 3" }
                      ]
                    }
                  },
                  "Type": {
                    "label": "Değişiklik Tipi",
                    "type": "select",
                    "required": true,
                    "description": "Nasıl değiştirileceği",
                    "options": {
                      "items": [
                        { "value": 1, "label": "Sabit Değer Ata" },
                        { "value": 2, "label": "Şu Anki Tarih/Saat Ata" },
                        { "value": 3, "label": "Mevcut Kullanıcı Ata" },
                        { "value": 4, "label": "Hesaplanmış Değer Ata" }
                      ]
                    }
                  },
                  "Value": {
                    "label": "Değer",
                    "type": "textarea",
                    "required": false,
                    "description": "Atanacak değer veya hesaplama ifadesi. Template değişkenleri: {{NOW}}, {{CURRENT_USER_ID}}, {{TICKET_ID}}, {{TICKET.FieldName}}, {{USER.FieldName}}",
                    "placeholder": "Örnek: Yüksek öncelik - {{TICKET.Title}} - {{NOW}}"
                  }
                }
              }
            }
          ]
        }
      ]
    }
    """;

    public override async Task<RuleResult> ExecuteAsync(
        TransitionRule rule,
        TransitionContext context,
        CancellationToken cancellationToken)
    {
        try
        {
            var config = GetConfiguration<FieldModificationConfiguration>(rule);
            var result = RuleResult.Success();

            foreach (var modification in config.Modifications)
            {
                var newValue = await CalculateNewValueAsync(modification, context);
                result.ModifiedFields[modification.FieldName] = newValue;

                logger.LogInformation("Field {FieldName} modified to {NewValue} for ticket {TicketId}",
                    modification.FieldName, newValue, context.TicketId);
            }

            return result;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Field modification rule execution failed for rule {RuleId}", rule.Id);
            return RuleResult.Failure("Alan değiştirme sırasında hata oluştu");
        }
    }

    private async Task<object> CalculateNewValueAsync(FieldModification modification, TransitionContext context)
    {
        await Task.CompletedTask;

        return modification.Type switch
        {
            ModificationType.SetValue => modification.Value,
            ModificationType.SetCurrentDateTime => DateTime.UtcNow,
            ModificationType.SetCurrentUser => context.UserId,
            ModificationType.SetCalculatedValue => CalculateValue(modification.Value, context),
            _ => modification.Value
        };
    }

    private object CalculateValue(string expression, TransitionContext context)
    {
        // Template değişkenlerini değiştir
        var result = expression
            .Replace("{{NOW}}", DateTime.UtcNow.ToString())
            .Replace("{{CURRENT_USER_ID}}", context.UserId.ToString())
            .Replace("{{TICKET_ID}}", context.TicketId.ToString())
            .Replace("{{FROM_NODE_ID}}", context.FromNode.Id.ToString())
            .Replace("{{TO_NODE_ID}}", context.ToNode.Id.ToString());

        // Ticket alanlarından değer al
        foreach (var kvp in context.TicketData)
        {
            result = result.Replace($"{{{{TICKET.{kvp.Key}}}}}", kvp.Value?.ToString() ?? string.Empty);
        }

        // User alanlarından değer al
        foreach (var kvp in context.UserData)
        {
            result = result.Replace($"{{{{USER.{kvp.Key}}}}}", kvp.Value?.ToString() ?? string.Empty);
        }

        return result;
    }
}
