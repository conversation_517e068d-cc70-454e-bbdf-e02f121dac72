using Microsoft.Extensions.Logging;
using Requests.Application.Rules.Engine.Configurations;
using Requests.Domain;
using Requests.Domain.Enums;

namespace Requests.Application.Rules.Engine.Handlers;

public class AuthorizationRuleHandler(
    ILogger<AuthorizationRuleHandler> logger
) : BaseRuleHandler
{
    public override RuleType RuleType => RuleType.Authorization;

    public static readonly string FormConfigurationJson = """
    {
      "title": "Yetkilendirme Kuralı",
      "description": "Bu geçişi yapabilecek kullanıcıları ve rolleri belirler",
      "sections": [
        {
          "title": "Rol Bazlı Yetkilendirme",
          "fields": [
            {
              "name": "AllowedRoles",
              "label": "İzin Verilen Roller",
              "type": "multiselect",
              "required": false,
              "description": "Bu geçişi yapabilecek roller",
              "options": {
                "dataSource": "roles",
                "valueField": "name",
                "labelField": "name"
              }
            }
          ]
        },
        {
          "title": "<PERSON>llanıcı Bazlı Yetkilendirme",
          "fields": [
            {
              "name": "AllowedUsers",
              "label": "İzin Verilen Kullanıcılar",
              "type": "multiselect",
              "required": false,
              "description": "Bu geçişi yapabilecek belirli kullanıcılar",
              "options": {
                "dataSource": "users",
                "valueField": "id",
                "labelField": "fullName"
              }
            }
          ]
        },
        {
          "title": "İzin Bazlı Yetkilendirme",
          "fields": [
            {
              "name": "RequiredPermissions",
              "label": "Gerekli İzinler",
              "type": "multiselect",
              "required": false,
              "description": "Bu geçiş için gerekli izinler",
              "options": {
                "dataSource": "permissions",
                "valueField": "name",
                "labelField": "displayName"
              }
            }
          ]
        },
        {
          "title": "Departman Kısıtlaması",
          "fields": [
            {
              "name": "DepartmentRestriction.Field",
              "label": "Departman Alanı",
              "type": "select",
              "required": false,
              "description": "Departman bilgisinin alınacağı alan",
              "options": {
                "items": [
                  { "value": "AssignedDepartmentId", "label": "Atanan Departman" },
                  { "value": "ReporterDepartmentId", "label": "Rapor Eden Departman" },
                  { "value": "OwnerDepartmentId", "label": "Sahip Departman" }
                ]
              }
            },
            {
              "name": "DepartmentRestriction.AllowSameDepartment",
              "label": "Aynı Departmana İzin Ver",
              "type": "checkbox",
              "required": false,
              "description": "Kullanıcının kendi departmanından geçiş yapmasına izin ver"
            },
            {
              "name": "DepartmentRestriction.AllowedDepartments",
              "label": "İzin Verilen Departmanlar",
              "type": "multiselect",
              "required": false,
              "description": "Bu geçişi yapabilecek departmanlar",
              "options": {
                "dataSource": "departments",
                "valueField": "id",
                "labelField": "name"
              }
            }
          ]
        }
      ]
    }
    """;

    public override async Task<RuleResult> ExecuteAsync(
        TransitionRule rule,
        TransitionContext context,
        CancellationToken cancellationToken)
    {
        try
        {
            var config = GetConfiguration<AuthorizationConfiguration>(rule);

            // Role bazlı kontrol
            if (config.AllowedRoles?.Any() == true)
            {
                // TODO: User service'den kullanıcı rollerini al
                // var userRoles = await userService.GetUserRolesAsync(context.UserId);
                // if (!userRoles.Any(r => config.AllowedRoles.Contains(r)))
                // {
                //     return RuleResult.Failure("Bu geçiş için yetkiniz bulunmamaktadır", isBlocking: true);
                // }

                // Şimdilik basit kontrol
                logger.LogInformation("Role based authorization check for user {UserId}", context.UserId);
            }

            // Kullanıcı bazlı kontrol
            if (config.AllowedUsers?.Any() == true)
            {
                if (!config.AllowedUsers.Contains(context.UserId.ToString()))
                {
                    return RuleResult.Failure("Bu geçişi yapma yetkiniz bulunmamaktadır", isBlocking: true);
                }
            }

            // Department kısıtlaması
            if (config.DepartmentRestriction != null)
            {
                var isAuthorized = await ValidateDepartmentRestrictionAsync(config.DepartmentRestriction, context);
                if (!isAuthorized)
                {
                    return RuleResult.Failure("Departman kısıtlaması nedeniyle geçiş yapılamaz", isBlocking: true);
                }
            }

            return RuleResult.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Authorization rule execution failed for rule {RuleId}", rule.Id);
            return RuleResult.Failure("Yetkilendirme kontrolü sırasında hata oluştu", isBlocking: true);
        }
    }

    private async Task<bool> ValidateDepartmentRestrictionAsync(
        DepartmentRestriction restriction,
        TransitionContext context)
    {
        // TODO: Department validation logic
        await Task.CompletedTask;

        if (restriction.AllowSameDepartment)
        {
            // Aynı departmandan kontrol et
            logger.LogInformation("Same department check for user {UserId}", context.UserId);
        }

        if (restriction.AllowedDepartments?.Any() == true)
        {
            // İzin verilen departmanları kontrol et
            logger.LogInformation("Allowed departments check for user {UserId}", context.UserId);
        }

        return true; // Şimdilik true dön
    }
}
