using Shared.Application;

namespace Requests.Application.Services;

public interface IWorkflowValidationService
{
    Task<Result> ValidateFlowAsync(Guid flowId, CancellationToken cancellationToken = default);
    Task<Result> ValidateNodeAsync(Guid nodeId, CancellationToken cancellationToken = default);
    Task<Result> ValidateTransitionAsync(Guid transitionId, CancellationToken cancellationToken = default);
    Task<Result> ValidateFlowIntegrityAsync(Guid flowId, CancellationToken cancellationToken = default);
    Task<Result> ValidateNodeDeletionAsync(Guid nodeId, CancellationToken cancellationToken = default);
    Task<Result> ValidateCircularDependencyAsync(Guid fromNodeId, Guid toNodeId, CancellationToken cancellationToken = default);
}
