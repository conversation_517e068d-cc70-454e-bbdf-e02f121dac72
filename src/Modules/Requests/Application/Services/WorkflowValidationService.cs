using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Shared.Application;

namespace Requests.Application.Services;

public class WorkflowValidationService(IRequestsDbContext context) : IWorkflowValidationService
{
    public async Task<Result> ValidateFlowAsync(Guid flowId, CancellationToken cancellationToken = default)
    {
        var flow = await context.Flows
            .Include(f => f.Nodes)
            .FirstOrDefaultAsync(f => f.Id == flowId, cancellationToken);

        if (flow is null)
        {
            return Result.Failure("Flow bulunamadı");
        }

        if (!flow.Nodes.Any())
        {
            return Result.Failure("Flow en az bir node içermelidir");
        }

        return Result.Success();
    }

    public async Task<Result> ValidateNodeAsync(Guid nodeId, CancellationToken cancellationToken = default)
    {
        var node = await context.Nodes
            .Include(n => n.Flow)
            .FirstOrDefaultAsync(n => n.Id == nodeId, cancellationToken);

        if (node is null)
        {
            return Result.Failure("Node bulunamadı");
        }

        return Result.Success();
    }

    public async Task<Result> ValidateTransitionAsync(Guid transitionId, CancellationToken cancellationToken = default)
    {
        var transition = await context.Transitions
            .Include(t => t.FromNode)
            .Include(t => t.ToNode)
            .FirstOrDefaultAsync(t => t.Id == transitionId, cancellationToken);

        if (transition is null)
        {
            return Result.Failure("Transition bulunamadı");
        }

        if (transition.FromNode.FlowId != transition.ToNode.FlowId)
        {
            return Result.Failure("Transition'ın kaynak ve hedef node'ları aynı flow içerisinde olmalıdır");
        }

        return Result.Success();
    }

    public async Task<Result> ValidateFlowIntegrityAsync(Guid flowId, CancellationToken cancellationToken = default)
    {
        var flow = await context.Flows
            .Include(f => f.Nodes)
                .ThenInclude(n => n.FromTransitions)
            .Include(f => f.Nodes)
                .ThenInclude(n => n.ToTransitions)
            .FirstOrDefaultAsync(f => f.Id == flowId, cancellationToken);

        if (flow is null)
        {
            return Result.Failure("Flow bulunamadı");
        }

        // Check for orphaned nodes (nodes without any transitions)
        var orphanedNodes = flow.Nodes
            .Where(n => !n.FromTransitions.Any() && !n.ToTransitions.Any())
            .ToList();

        if (orphanedNodes.Count > 1)
        {
            return Result.Failure($"Flow içerisinde bağlantısız {orphanedNodes.Count} node bulunmaktadır");
        }

        return Result.Success();
    }

    public async Task<Result> ValidateNodeDeletionAsync(Guid nodeId, CancellationToken cancellationToken = default)
    {
        var node = await context.Nodes
            .Include(n => n.FromTransitions)
            .Include(n => n.ToTransitions)
            .FirstOrDefaultAsync(n => n.Id == nodeId, cancellationToken);

        if (node is null)
        {
            return Result.Failure("Node bulunamadı");
        }

        if (node.FromTransitions.Any() || node.ToTransitions.Any())
        {
            return Result.Failure("Bu node'a bağlı transition'lar bulunmaktadır. Önce transition'ları siliniz.");
        }

        return Result.Success();
    }

    public async Task<Result> ValidateCircularDependencyAsync(Guid fromNodeId, Guid toNodeId, CancellationToken cancellationToken = default)
    {
        if (fromNodeId == toNodeId)
        {
            return Result.Failure("Bir node kendisine transition oluşturamaz");
        }

        // Check if creating this transition would create a circular dependency
        var hasPath = await HasPathAsync(toNodeId, fromNodeId, cancellationToken);
        if (hasPath)
        {
            return Result.Failure("Bu transition döngüsel bağımlılık oluşturacaktır");
        }

        return Result.Success();
    }

    private async Task<bool> HasPathAsync(Guid fromNodeId, Guid toNodeId, CancellationToken cancellationToken)
    {
        var visited = new HashSet<Guid>();
        var queue = new Queue<Guid>();
        queue.Enqueue(fromNodeId);

        while (queue.Count > 0)
        {
            var currentNodeId = queue.Dequeue();
            
            if (currentNodeId == toNodeId)
            {
                return true;
            }

            if (visited.Contains(currentNodeId))
            {
                continue;
            }

            visited.Add(currentNodeId);

            var nextNodes = await context.Transitions
                .Where(t => t.FromNodeId == currentNodeId)
                .Select(t => t.ToNodeId)
                .ToListAsync(cancellationToken);

            foreach (var nextNodeId in nextNodes)
            {
                if (!visited.Contains(nextNodeId))
                {
                    queue.Enqueue(nextNodeId);
                }
            }
        }

        return false;
    }
}
