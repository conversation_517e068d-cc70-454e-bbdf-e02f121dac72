using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Requests.Application.Transitions.CreateTransition;

internal sealed class CreateTransitionEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v1/requests/transitions", async (
            CreateTransitionCommand command,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var result = await mediator.Send(command, cancellationToken);
            return result.Match(
                id => Results.Created($"/api/v1/requests/transitions/{id}", id),
                CustomResults.Problem);
        })
        .WithTags("Requests.Transitions")
        .WithGroupName("apiv1")
        .RequireAuthorization("Requests.Transitions");
    }
}
