using FluentValidation;

namespace Requests.Application.Transitions.CreateTransition;

public class CreateTransitionCommandValidator : AbstractValidator<CreateTransitionCommand>
{
    public CreateTransitionCommandValidator()
    {
        RuleFor(x => x.FromNodeId)
            .NotEmpty()
            .WithMessage("Kaynak Node ID boş olamaz");

        RuleFor(x => x.ToNodeId)
            .NotEmpty()
            .WithMessage("Hedef Node ID boş olamaz");

        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("Transition adı boş olamaz")
            .MaximumLength(100)
            .WithMessage("Transition adı maksimum 100 karakter olabilir");

        RuleFor(x => x)
            .Must(x => x.FromNodeId != x.ToNodeId)
            .WithMessage("Kaynak ve hedef node aynı olamaz");
    }
}
