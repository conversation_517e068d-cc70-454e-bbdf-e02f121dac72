using FluentValidation;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;

namespace Requests.Application.Transitions.CreateTransition;

public class CreateTransitionCommandValidator : AbstractValidator<CreateTransitionCommand>
{
    private readonly IRequestsDbContext _context;

    public CreateTransitionCommandValidator(IRequestsDbContext context)
    {
        _context = context;

        RuleFor(x => x.FromNodeId)
            .NotEmpty()
            .WithMessage("Kaynak Node ID boş olamaz")
            .MustAsync(NodeExists)
            .WithMessage("Kaynak node bulunamadı");

        RuleFor(x => x.ToNodeId)
            .NotEmpty()
            .WithMessage("Hedef Node ID boş olamaz")
            .MustAsync(NodeExists)
            .WithMessage("Hedef node bulunamadı");

        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("Transition adı boş olamaz")
            .MaximumLength(100)
            .WithMessage("Transition adı maksimum 100 karakter olabilir");

        RuleFor(x => x)
            .Must(x => x.FromNodeId != x.ToNodeId)
            .WithMessage("Kaynak ve hedef node aynı olamaz")
            .MustAsync(NodesInSameFlow)
            .WithMessage("Kaynak ve hedef node'lar aynı flow içerisinde olmalıdır")
            .MustAsync(NoCircularDependency)
            .WithMessage("Bu transition döngüsel bağımlılık oluşturacaktır")
            .MustAsync(TransitionNotExists)
            .WithMessage("Bu node'lar arasında aynı isimde bir transition zaten mevcut");
    }

    private async Task<bool> NodeExists(Guid nodeId, CancellationToken cancellationToken)
    {
        return await _context.Nodes.AnyAsync(n => n.Id == nodeId, cancellationToken);
    }

    private async Task<bool> NodesInSameFlow(CreateTransitionCommand command, CancellationToken cancellationToken)
    {
        var fromNode = await _context.Nodes.FirstOrDefaultAsync(n => n.Id == command.FromNodeId, cancellationToken);
        var toNode = await _context.Nodes.FirstOrDefaultAsync(n => n.Id == command.ToNodeId, cancellationToken);

        if (fromNode == null || toNode == null)
            return true; // Will be caught by NodeExists validation

        return fromNode.FlowId == toNode.FlowId;
    }

    private async Task<bool> NoCircularDependency(CreateTransitionCommand command, CancellationToken cancellationToken)
    {
        return !await HasPathAsync(command.ToNodeId, command.FromNodeId, cancellationToken);
    }

    private async Task<bool> TransitionNotExists(CreateTransitionCommand command, CancellationToken cancellationToken)
    {
        return !await _context.Transitions.AnyAsync(t =>
            t.FromNodeId == command.FromNodeId &&
            t.ToNodeId == command.ToNodeId &&
            t.Name == command.Name, cancellationToken);
    }

    private async Task<bool> HasPathAsync(Guid fromNodeId, Guid toNodeId, CancellationToken cancellationToken)
    {
        var visited = new HashSet<Guid>();
        var queue = new Queue<Guid>();
        queue.Enqueue(fromNodeId);

        while (queue.Count > 0)
        {
            var currentNodeId = queue.Dequeue();

            if (currentNodeId == toNodeId)
            {
                return true;
            }

            if (visited.Contains(currentNodeId))
            {
                continue;
            }

            visited.Add(currentNodeId);

            var nextNodes = await _context.Transitions
                .Where(t => t.FromNodeId == currentNodeId)
                .Select(t => t.ToNodeId)
                .ToListAsync(cancellationToken);

            foreach (var nextNodeId in nextNodes)
            {
                if (!visited.Contains(nextNodeId))
                {
                    queue.Enqueue(nextNodeId);
                }
            }
        }

        return false;
    }
}
