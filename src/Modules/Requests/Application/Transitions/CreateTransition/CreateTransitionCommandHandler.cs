using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Requests.Application.Services;
using Requests.Domain;
using Shared.Application;

namespace Requests.Application.Transitions.CreateTransition;

public class CreateTransitionCommandHandler(IRequestsDbContext context, IWorkflowValidationService validationService)
    : IRequestHandler<CreateTransitionCommand, Result<Guid>>
{
    public async Task<Result<Guid>> Handle(CreateTransitionCommand request, CancellationToken cancellationToken)
    {
        var fromNode = await context.Nodes
            .FirstOrDefaultAsync(n => n.Id == request.FromNodeId, cancellationToken);

        if (fromNode is null)
        {
            return Result.Failure<Guid>("Kaynak node bulunamadı");
        }

        var toNode = await context.Nodes
            .FirstOrDefaultAsync(n => n.Id == request.ToNodeId, cancellationToken);

        if (toNode is null)
        {
            return Result.Failure<Guid>("Hedef node bulunamadı");
        }

        if (fromNode.FlowId != toNode.FlowId)
        {
            return Result.Failure<Guid>("Kaynak ve hedef node'lar aynı flow içerisinde olmalıdır");
        }

        // Validate circular dependency
        var circularValidation = await validationService.ValidateCircularDependencyAsync(request.FromNodeId, request.ToNodeId, cancellationToken);
        if (!circularValidation.IsSuccess)
        {
            return Result.Failure<Guid>(circularValidation.Error);
        }

        var existingTransition = await context.Transitions
            .FirstOrDefaultAsync(t => t.FromNodeId == request.FromNodeId &&
                                    t.ToNodeId == request.ToNodeId &&
                                    t.Name == request.Name, cancellationToken);

        if (existingTransition is not null)
        {
            return Result.Failure<Guid>("Bu node'lar arasında aynı isimde bir transition zaten mevcut");
        }

        var transition = new Transition
        {
            Id = Guid.NewGuid(),
            FromNodeId = request.FromNodeId,
            ToNodeId = request.ToNodeId,
            Name = request.Name
        };

        context.Transitions.Add(transition);
        await context.SaveChangesAsync(cancellationToken);

        return Result.Success(transition.Id);
    }
}
