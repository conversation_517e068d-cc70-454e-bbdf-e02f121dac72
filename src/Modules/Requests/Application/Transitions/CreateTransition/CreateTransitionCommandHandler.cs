using MediatR;
using Requests.Application.Abstractions;
using Requests.Domain;
using Shared.Application;

namespace Requests.Application.Transitions.CreateTransition;

public class CreateTransitionCommandHandler(IRequestsDbContext context)
    : IRequestHandler<CreateTransitionCommand, Result<Guid>>
{
    public async Task<Result<Guid>> <PERSON>le(CreateTransitionCommand request, CancellationToken cancellationToken)
    {
        var transition = new Transition
        {
            Id = Guid.NewGuid(),
            FromNodeId = request.FromNodeId,
            ToNodeId = request.ToNodeId,
            Name = request.Name
        };

        context.Transitions.Add(transition);
        await context.SaveChangesAsync(cancellationToken);

        return Result.Success(transition.Id);
    }
}
