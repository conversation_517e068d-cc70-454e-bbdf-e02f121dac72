using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Shared.Application;

namespace Requests.Application.Transitions.DeleteTransition;

public class DeleteTransitionCommandHandler(IRequestsDbContext context)
    : IRequestHandler<DeleteTransitionCommand, Result>
{
    public async Task<Result> Handle(DeleteTransitionCommand request, CancellationToken cancellationToken)
    {
        var transition = await context.Transitions
            .FirstOrDefaultAsync(t => t.Id == request.Id, cancellationToken);

        if (transition is null)
        {
            return Result.Failure("Transition bulunamadı");
        }

        context.Transitions.Remove(transition);
        await context.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
