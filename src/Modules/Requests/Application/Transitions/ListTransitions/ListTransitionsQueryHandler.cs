using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Shared.Application;

namespace Requests.Application.Transitions.ListTransitions;

public class ListTransitionsQueryHandler(IRequestsDbContext context)
    : IRequestHandler<ListTransitionsQuery, PagedResult<TransitionListItemDto>>
{
    public async Task<PagedResult<TransitionListItemDto>> Handle(ListTransitionsQuery request, CancellationToken cancellationToken)
    {
        var query = context.Transitions
            .Include(t => t.FromNode)
            .Include(t => t.ToNode)
            .AsQueryable();

        if (request.FlowId.HasValue)
        {
            query = query.Where(t => t.FromNode.FlowId == request.FlowId.Value);
        }

        if (request.NodeId.HasValue)
        {
            query = query.Where(t => t.FromNodeId == request.NodeId.Value || t.ToNodeId == request.NodeId.Value);
        }

        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            query = query.Where(t => t.Name.Contains(request.SearchTerm) ||
                                   t.FromNode.Name.Contains(request.SearchTerm) ||
                                   t.ToNode.Name.Contains(request.SearchTerm));
        }

        var totalCount = await query.CountAsync(cancellationToken);

        var transitions = await query
            .OrderBy(t => t.FromNode.Name)
            .ThenBy(t => t.ToNode.Name)
            .ThenBy(t => t.Name)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(t => new TransitionListItemDto
            {
                Id = t.Id,
                FromNodeId = t.FromNodeId,
                ToNodeId = t.ToNodeId,
                Name = t.Name,
                InsertDate = t.InsertDate,
                UpdateDate = t.UpdateDate,
                FromNodeName = t.FromNode.Name,
                ToNodeName = t.ToNode.Name
            })
            .ToListAsync(cancellationToken);

        return new PagedResult<TransitionListItemDto>(transitions)
        {
            PageNumber = request.PageNumber,
            PageSize = request.PageSize,
            Count = totalCount,
            FilteredCount = totalCount
        };
    }
}
