using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Requests.Application.Transitions.ListTransitions;

internal sealed class ListTransitionsEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/v1/requests/transitions", async (
            Guid? flowId,
            Guid? nodeId,
            int pageNumber,
            int pageSize,
            string? searchTerm,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var query = new ListTransitionsQuery
            {
                FlowId = flowId,
                NodeId = nodeId,
                PageNumber = pageNumber,
                PageSize = pageSize,
                SearchTerm = searchTerm
            };
            var result = await mediator.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("Requests.Transitions")
        .WithGroupName("apiv1")
        .RequireAuthorization("Requests.Transitions");
    }
}
