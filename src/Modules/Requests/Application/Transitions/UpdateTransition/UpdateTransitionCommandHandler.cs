using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Shared.Application;

namespace Requests.Application.Transitions.UpdateTransition;

public class UpdateTransitionCommandHandler(IRequestsDbContext context)
    : IRequestHandler<UpdateTransitionCommand, Result>
{
    public async Task<Result> Handle(UpdateTransitionCommand request, CancellationToken cancellationToken)
    {
        var transition = await context.Transitions
            .FirstOrDefaultAsync(t => t.Id == request.Id, cancellationToken);

        if (transition is null)
        {
            return Result.Failure("Transition bulunamadı");
        }

        var existingTransition = await context.Transitions
            .FirstOrDefaultAsync(t => t.FromNodeId == transition.FromNodeId && 
                                    t.ToNodeId == transition.ToNodeId && 
                                    t.Name == request.Name && 
                                    t.Id != request.Id, cancellationToken);

        if (existingTransition is not null)
        {
            return Result.Failure("Bu node'lar arasında aynı isimde başka bir transition zaten mevcut");
        }

        transition.Name = request.Name;

        await context.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
