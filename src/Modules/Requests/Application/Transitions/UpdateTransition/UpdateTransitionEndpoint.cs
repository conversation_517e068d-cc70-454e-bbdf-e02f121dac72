using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Requests.Application.Transitions.UpdateTransition;

internal sealed class UpdateTransitionEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPut("/api/v1/requests/transitions/{id}", async (
            Guid id,
            UpdateTransitionRequest request,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var command = new UpdateTransitionCommand(id, request.Name);
            var result = await mediator.Send(command, cancellationToken);
            return result.Match(Results.NoContent, CustomResults.Problem);
        })
        .WithTags("Requests.Transitions")
        .WithGroupName("apiv1")
        .RequireAuthorization("Requests.Transitions");
    }
}

public record UpdateTransitionRequest(string Name);
