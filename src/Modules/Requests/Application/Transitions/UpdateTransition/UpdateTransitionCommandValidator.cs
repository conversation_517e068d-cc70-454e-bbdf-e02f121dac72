using FluentValidation;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;

namespace Requests.Application.Transitions.UpdateTransition;

public class UpdateTransitionCommandValidator : AbstractValidator<UpdateTransitionCommand>
{
    private readonly IRequestsDbContext _context;

    public UpdateTransitionCommandValidator(IRequestsDbContext context)
    {
        _context = context;

        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage("Transition ID boş olamaz")
            .MustAsync(TransitionExists)
            .WithMessage("Transition bulunamadı");

        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("Transition adı boş olamaz")
            .MaximumLength(100)
            .WithMessage("Transition adı maksimum 100 karakter olabilir");

        RuleFor(x => x)
            .MustAsync(TransitionNameNotExists)
            .WithMessage("Bu node'lar arasında aynı isimde başka bir transition zaten mevcut");
    }

    private async Task<bool> TransitionExists(Guid transitionId, CancellationToken cancellationToken)
    {
        return await _context.Transitions.AnyAsync(t => t.Id == transitionId, cancellationToken);
    }

    private async Task<bool> TransitionNameNotExists(UpdateTransitionCommand command, CancellationToken cancellationToken)
    {
        var transition = await _context.Transitions.FirstOrDefaultAsync(t => t.Id == command.Id, cancellationToken);
        if (transition == null)
            return true; // Will be caught by TransitionExists validation

        return !await _context.Transitions.AnyAsync(t =>
            t.FromNodeId == transition.FromNodeId &&
            t.ToNodeId == transition.ToNodeId &&
            t.Name == command.Name &&
            t.Id != command.Id, cancellationToken);
    }
}
