using FluentValidation;

namespace Requests.Application.Transitions.UpdateTransition;

public class UpdateTransitionCommandValidator : AbstractValidator<UpdateTransitionCommand>
{
    public UpdateTransitionCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage("Transition ID boş olamaz");

        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("Transition adı boş olamaz")
            .MaximumLength(100)
            .WithMessage("Transition adı maksimum 100 karakter olabilir");
    }
}
