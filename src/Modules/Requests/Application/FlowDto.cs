namespace Requests.Application;

public class FlowDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime InsertDate { get; set; }
    public DateTime? UpdateDate { get; set; }
    public List<NodeDto> Nodes { get; set; } = new();
}

public class FlowListItemDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime InsertDate { get; set; }
    public DateTime? UpdateDate { get; set; }
    public int NodeCount { get; set; }
}
