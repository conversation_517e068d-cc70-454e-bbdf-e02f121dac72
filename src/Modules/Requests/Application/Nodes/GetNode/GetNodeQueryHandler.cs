using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Shared.Application;

namespace Requests.Application.Nodes.GetNode;

public class GetNodeQueryHandler(IRequestsDbContext context)
    : IRequestHandler<GetNodeQuery, Result<NodeDto>>
{
    public async Task<Result<NodeDto>> <PERSON>le(GetNodeQuery request, CancellationToken cancellationToken)
    {
        var node = await context.Nodes
            .Include(n => n.FromTransitions)
                .ThenInclude(t => t.ToNode)
            .Include(n => n.ToTransitions)
                .ThenInclude(t => t.FromNode)
            .FirstOrDefaultAsync(n => n.Id == request.Id, cancellationToken);

        if (node is null)
        {
            return Result.Failure<NodeDto>("Node bulunamadı");
        }

        var nodeDto = new NodeDto
        {
            Id = node.Id,
            FlowId = node.FlowId,
            Name = node.Name,
            NodeType = node.NodeType.ToString(),
            InsertDate = node.InsertDate,
            UpdateDate = node.UpdateDate,
            FromTransitions = node.FromTransitions.Select(t => new TransitionDto
            {
                Id = t.Id,
                FromNodeId = t.FromNodeId,
                ToNodeId = t.ToNodeId,
                Name = t.Name,
                InsertDate = t.InsertDate,
                UpdateDate = t.UpdateDate,
                ToNodeName = t.ToNode.Name
            }).ToList(),
            ToTransitions = node.ToTransitions.Select(t => new TransitionDto
            {
                Id = t.Id,
                FromNodeId = t.FromNodeId,
                ToNodeId = t.ToNodeId,
                Name = t.Name,
                InsertDate = t.InsertDate,
                UpdateDate = t.UpdateDate,
                FromNodeName = t.FromNode.Name
            }).ToList()
        };

        return Result.Success(nodeDto);
    }
}
