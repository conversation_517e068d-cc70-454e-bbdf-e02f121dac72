using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Requests.Domain;
using Shared.Endpoints;

namespace Requests.Application.Nodes.UpdateNode;

internal sealed class UpdateNodeEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPut("/api/v1/requests/nodes/{id}", async (
            Guid id,
            UpdateNodeRequest request,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var command = new UpdateNodeCommand(id, request.Name, request.NodeType);
            var result = await mediator.Send(command, cancellationToken);
            return result.Match(Results.NoContent, CustomResults.Problem);
        })
        .WithTags("Requests.Nodes")
        .WithGroupName("apiv1")
        .RequireAuthorization("Requests.Nodes");
    }
}

public record UpdateNodeRequest(string Name, NodeType NodeType);
