using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Shared.Application;

namespace Requests.Application.Nodes.UpdateNode;

public class UpdateNodeCommandHandler(IRequestsDbContext context)
    : IRequestHandler<UpdateNodeCommand, Result>
{
    public async Task<Result> Handle(UpdateNodeCommand request, CancellationToken cancellationToken)
    {
        var node = await context.Nodes
            .FirstOrDefaultAsync(n => n.Id == request.Id, cancellationToken);

        if (node is null)
        {
            return Result.Failure("Node bulunamadı");
        }

        var existingNode = await context.Nodes
            .FirstOrDefaultAsync(n => n.FlowId == node.FlowId && n.Name == request.Name && n.Id != request.Id, cancellationToken);

        if (existingNode is not null)
        {
            return Result.Failure("Bu flow içerisinde aynı isimde başka bir node zaten mevcut");
        }

        node.Name = request.Name;
        node.NodeType = request.NodeType;

        await context.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
