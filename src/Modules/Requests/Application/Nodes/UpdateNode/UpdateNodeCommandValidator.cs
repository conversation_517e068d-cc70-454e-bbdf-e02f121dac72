using FluentValidation;

namespace Requests.Application.Nodes.UpdateNode;

public class UpdateNodeCommandValidator : AbstractValidator<UpdateNodeCommand>
{
    public UpdateNodeCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage("Node ID boş olamaz");

        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("Node adı boş olamaz")
            .MaximumLength(100)
            .WithMessage("Node adı maksimum 100 karakter olabilir");

        RuleFor(x => x.NodeType)
            .IsInEnum()
            .WithMessage("Geçerli bir NodeType seçiniz");
    }
}
