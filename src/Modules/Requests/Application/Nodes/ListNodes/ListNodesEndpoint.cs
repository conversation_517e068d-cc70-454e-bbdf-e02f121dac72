using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Requests.Application.Nodes.ListNodes;

internal sealed class ListNodesEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/v1/requests/nodes/flow/{flowId}", async (
            Guid flowId,
            int pageNumber,
            int pageSize,
            string? searchTerm,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var query = new ListNodesQuery
            {
                FlowId = flowId,
                PageNumber = pageNumber,
                PageSize = pageSize,
                SearchTerm = searchTerm
            };
            var result = await mediator.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("Requests.Nodes")
        .WithGroupName("apiv1")
        .RequireAuthorization("Requests.Nodes");
    }
}
