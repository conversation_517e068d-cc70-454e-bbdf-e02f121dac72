using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Shared.Application;

namespace Requests.Application.Nodes.ListNodes;

public class ListNodesQueryHandler(IRequestsDbContext context)
    : IRequestHandler<ListNodesQuery, PagedResult<NodeListItemDto>>
{
    public async Task<PagedResult<NodeListItemDto>> Handle(ListNodesQuery request, CancellationToken cancellationToken)
    {
        var query = context.Nodes
            .Where(n => n.FlowId == request.FlowId);

        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            query = query.Where(n => n.Name.Contains(request.SearchTerm));
        }

        var totalCount = await query.CountAsync(cancellationToken);

        var nodes = await query
            .Include(n => n.FromTransitions)
            .Include(n => n.ToTransitions)
            .OrderBy(n => n.Name)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(n => new NodeListItemDto
            {
                Id = n.Id,
                FlowId = n.FlowId,
                Name = n.Name,
                NodeType = n.NodeType.ToString(),
                InsertDate = n.InsertDate,
                UpdateDate = n.UpdateDate,
                FromTransitionCount = n.FromTransitions.Count,
                ToTransitionCount = n.ToTransitions.Count
            })
            .ToListAsync(cancellationToken);

        return new PagedResult<NodeListItemDto>(nodes)
        {
            PageNumber = request.PageNumber,
            PageSize = request.PageSize,
            Count = totalCount,
            FilteredCount = totalCount
        };
    }
}
