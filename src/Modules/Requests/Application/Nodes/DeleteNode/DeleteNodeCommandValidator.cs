using FluentValidation;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;

namespace Requests.Application.Nodes.DeleteNode;

public class DeleteNodeCommandValidator : AbstractValidator<DeleteNodeCommand>
{
    private readonly IRequestsDbContext _context;

    public DeleteNodeCommandValidator(IRequestsDbContext context)
    {
        _context = context;

        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage("Node ID boş olamaz")
            .MustAsync(NodeExists)
            .WithMessage("Node bulunamadı")
            .MustAsync(NodeHasNoTransitions)
            .WithMessage("Bu node'a bağlı transition'lar bulunmaktadır. Önce transition'ları siliniz.");
    }

    private async Task<bool> NodeExists(Guid nodeId, CancellationToken cancellationToken)
    {
        return await _context.Nodes.AnyAsync(n => n.Id == nodeId, cancellationToken);
    }

    private async Task<bool> NodeHasNoTransitions(Guid nodeId, CancellationToken cancellationToken)
    {
        var hasFromTransitions = await _context.Transitions.AnyAsync(t => t.FromNodeId == nodeId, cancellationToken);
        var hasToTransitions = await _context.Transitions.AnyAsync(t => t.ToNodeId == nodeId, cancellationToken);
        
        return !hasFromTransitions && !hasToTransitions;
    }
}
