using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Requests.Application.Services;
using Shared.Application;

namespace Requests.Application.Nodes.DeleteNode;

public class DeleteNodeCommandHandler(IRequestsDbContext context, IWorkflowValidationService validationService)
    : IRequestHandler<DeleteNodeCommand, Result>
{
    public async Task<Result> Handle(DeleteNodeCommand request, CancellationToken cancellationToken)
    {
        var node = await context.Nodes
            .Include(n => n.FromTransitions)
            .Include(n => n.ToTransitions)
            .FirstOrDefaultAsync(n => n.Id == request.Id, cancellationToken);

        if (node is null)
        {
            return Result.Failure("Node bulunamadı");
        }

        // Validate node deletion
        var deletionValidation = await validationService.ValidateNodeDeletionAsync(request.Id, cancellationToken);
        if (!deletionValidation.IsSuccess)
        {
            return Result.Failure(deletionValidation.Error);
        }

        context.Nodes.Remove(node);
        await context.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
