using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Shared.Application;

namespace Requests.Application.Nodes.DeleteNode;

public class DeleteNodeCommandHandler(IRequestsDbContext context)
    : IRequestHandler<DeleteNodeCommand, Result>
{
    public async Task<Result> Handle(DeleteNodeCommand request, CancellationToken cancellationToken)
    {
        var node = await context.Nodes
            .FirstOrDefaultAsync(n => n.Id == request.Id, cancellationToken);

        if (node is null)
        {
            return Result.Failure("Node bulunamadı");
        }

        context.Nodes.Remove(node);
        await context.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
