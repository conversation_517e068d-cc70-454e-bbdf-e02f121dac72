using FluentValidation;

namespace Requests.Application.Nodes.CreateNode;

public class CreateNodeCommandValidator : AbstractValidator<CreateNodeCommand>
{
    public CreateNodeCommandValidator()
    {
        RuleFor(x => x.FlowId)
            .NotEmpty()
            .WithMessage("Flow ID boş olamaz");

        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("Node adı boş olamaz")
            .MaximumLength(100)
            .WithMessage("Node adı maksimum 100 karakter olabilir");

        RuleFor(x => x.NodeType)
            .IsInEnum()
            .WithMessage("Geçerli bir NodeType seçiniz");
    }
}
