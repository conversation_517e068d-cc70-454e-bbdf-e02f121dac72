using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Requests.Domain;
using Shared.Application;

namespace Requests.Application.Nodes.CreateNode;

public class CreateNodeCommandHandler(IRequestsDbContext context)
    : IRequestHandler<CreateNodeCommand, Result<Guid>>
{
    public async Task<Result<Guid>> Handle(CreateNodeCommand request, CancellationToken cancellationToken)
    {
        var flow = await context.Flows
            .FirstOrDefaultAsync(f => f.Id == request.FlowId, cancellationToken);

        if (flow is null)
        {
            return Result.Failure<Guid>("Flow bulunamadı");
        }

        var existingNode = await context.Nodes
            .FirstOrDefaultAsync(n => n.FlowId == request.FlowId && n.Name == request.Name, cancellationToken);

        if (existingNode is not null)
        {
            return Result.Failure<Guid>("Bu flow içerisinde aynı isimde bir node zaten mevcut");
        }

        var node = new Node
        {
            Id = Guid.NewGuid(),
            FlowId = request.FlowId,
            Name = request.Name,
            NodeType = request.NodeType
        };

        context.Nodes.Add(node);
        await context.SaveChangesAsync(cancellationToken);

        return Result.Success(node.Id);
    }
}
