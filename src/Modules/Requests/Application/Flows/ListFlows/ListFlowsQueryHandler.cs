using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Shared.Application;

namespace Requests.Application.Flows.ListFlows;

public class ListFlowsQueryHandler(IRequestsDbContext context)
    : IRequestHandler<ListFlowsQuery, PagedResult<FlowListItemDto>>
{
    public async Task<PagedResult<FlowListItemDto>> Handle(ListFlowsQuery request, CancellationToken cancellationToken)
    {
        var query = context.Flows.AsQueryable();

        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            query = query.Where(f => f.Name.Contains(request.SearchTerm) ||
                                f.Description.Contains(request.SearchTerm));
        }

        var totalCount = await query.CountAsync(cancellationToken);

        var flows = await query
            .Include(f => f.Nodes)
            .OrderBy(f => f.Name)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(f => new FlowListItemDto
            {
                Id = f.Id,
                Name = f.Name,
                Description = f.Description,
                InsertDate = f.InsertDate,
                UpdateDate = f.UpdateDate,
                NodeCount = f.Nodes.Count
            })
            .ToListAsync(cancellationToken);

        return new PagedResult<FlowListItemDto>(flows)
        {
            PageNumber = request.PageNumber,
            PageSize = request.PageSize,
            Count = totalCount,
            FilteredCount = totalCount
        };
    }
}
