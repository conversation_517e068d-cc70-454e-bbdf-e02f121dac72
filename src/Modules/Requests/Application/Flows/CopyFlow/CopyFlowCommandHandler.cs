using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Requests.Domain;
using Shared.Application;

namespace Requests.Application.Flows.CopyFlow;

public class CopyFlowCommandHandler(IRequestsDbContext context)
    : IRequestHandler<CopyFlowCommand, Result<Guid>>
{
    public async Task<Result<Guid>> Handle(CopyFlowCommand request, CancellationToken cancellationToken)
    {
        var sourceFlow = await context.Flows
            .Include(f => f.Nodes)
                .ThenInclude(n => n.FromTransitions)
            .FirstOrDefaultAsync(f => f.Id == request.SourceFlowId, cancellationToken);

        if (sourceFlow is null)
        {
            return Result.Failure<Guid>("Kaynak flow bulunamadı");
        }

        var existingFlow = await context.Flows
            .FirstOrDefaultAsync(f => f.Name == request.NewName, cancellationToken);

        if (existingFlow is not null)
        {
            return Result.Failure<Guid>("Bu isimde bir flow zaten mevcut");
        }

        var newFlow = new Flow
        {
            Id = Guid.NewGuid(),
            Name = request.NewName,
            Description = request.NewDescription ?? sourceFlow.Description
        };

        context.Flows.Add(newFlow);

        // Node mapping for transitions
        var nodeMapping = new Dictionary<Guid, Guid>();

        // Copy nodes
        foreach (var sourceNode in sourceFlow.Nodes)
        {
            var newNodeId = Guid.NewGuid();
            nodeMapping[sourceNode.Id] = newNodeId;

            var newNode = new Node
            {
                Id = newNodeId,
                FlowId = newFlow.Id,
                Name = sourceNode.Name,
                NodeType = sourceNode.NodeType
            };

            context.Nodes.Add(newNode);
        }

        await context.SaveChangesAsync(cancellationToken);

        // Copy transitions
        var allTransitions = sourceFlow.Nodes.SelectMany(n => n.FromTransitions).ToList();
        foreach (var sourceTransition in allTransitions)
        {
            var newTransition = new Transition
            {
                Id = Guid.NewGuid(),
                FromNodeId = nodeMapping[sourceTransition.FromNodeId],
                ToNodeId = nodeMapping[sourceTransition.ToNodeId],
                Name = sourceTransition.Name
            };

            context.Transitions.Add(newTransition);
        }

        await context.SaveChangesAsync(cancellationToken);

        return Result.Success(newFlow.Id);
    }
}
