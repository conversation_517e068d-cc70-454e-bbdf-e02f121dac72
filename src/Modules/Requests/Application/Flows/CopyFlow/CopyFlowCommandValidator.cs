using FluentValidation;

namespace Requests.Application.Flows.CopyFlow;

public class CopyFlowCommandValidator : AbstractValidator<CopyFlowCommand>
{
    public CopyFlowCommandValidator()
    {
        RuleFor(x => x.SourceFlowId)
            .NotEmpty()
            .WithMessage("Kaynak Flow ID boş olamaz");

        RuleFor(x => x.NewName)
            .NotEmpty()
            .WithMessage("Yeni Flow adı boş olamaz")
            .MaximumLength(100)
            .WithMessage("Flow adı maksimum 100 karakter olabilir");

        RuleFor(x => x.NewDescription)
            .MaximumLength(500)
            .WithMessage("Açıklama maksimum 500 karakter olabilir");
    }
}
