using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Requests.Application.Flows.CopyFlow;

internal sealed class CopyFlowEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v1/requests/flows/{id}/copy", async (
            Guid id,
            CopyFlowRequest request,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var command = new CopyFlowCommand(id, request.NewName, request.NewDescription);
            var result = await mediator.Send(command, cancellationToken);
            return result.Match(
                newId => Results.Created($"/api/v1/requests/flows/{newId}", newId),
                CustomResults.Problem);
        })
        .WithTags("Requests.Flows")
        .WithGroupName("apiv1")
        .RequireAuthorization("Requests.Flows");
    }
}

public record CopyFlowRequest(string NewName, string? NewDescription = null);
