using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Shared.Application;

namespace Requests.Application.Flows.DeleteFlow;

public class DeleteFlowCommandHandler(IRequestsDbContext context)
    : IRequestHandler<DeleteFlowCommand, Result>
{
    public async Task<Result> Handle(DeleteFlowCommand request, CancellationToken cancellationToken)
    {
        var flow = await context.Flows
            .FirstOrDefaultAsync(f => f.Id == request.Id, cancellationToken);

        if (flow is null)
        {
            return Result.Failure("Flow bulunamadı");
        }

        context.Flows.Remove(flow);
        await context.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
