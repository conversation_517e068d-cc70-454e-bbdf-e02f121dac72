using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Requests.Application.Flows.CreateFlow;

internal sealed class CreateFlowEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v1/requests/flows", async (
            CreateFlowCommand command,
            IMediator mediator,
            CancellationToken cancellationToken) =>
        {
            var result = await mediator.Send(command, cancellationToken);
            return result.Match(
                id => Results.Created($"/api/v1/requests/flows/{id}", id),
                CustomResults.Problem);
        })
        .WithTags("Requests.Flows")
        .WithGroupName("apiv1")
        .RequireAuthorization("Requests.Flows");
    }
}
