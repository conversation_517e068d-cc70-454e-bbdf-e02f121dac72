using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Requests.Domain;
using Shared.Application;

namespace Requests.Application.Flows.CreateFlow;

public class CreateFlowCommandHandler(IRequestsDbContext context)
    : IRequestHandler<CreateFlowCommand, Result<Guid>>
{
    public async Task<Result<Guid>> Handle(CreateFlowCommand request, CancellationToken cancellationToken)
    {
        var existingFlow = await context.Flows
            .FirstOrDefaultAsync(f => f.Name == request.Name, cancellationToken);

        if (existingFlow is not null)
        {
            return Result.Failure<Guid>("Bu isimde bir flow zaten mevcut");
        }

        var flow = new Flow
        {
            Id = Guid.NewGuid(),
            Name = request.Name,
            Description = request.Description ?? string.Empty
        };

        context.Flows.Add(flow);
        await context.SaveChangesAsync(cancellationToken);

        return Result.Success(flow.Id);
    }
}
