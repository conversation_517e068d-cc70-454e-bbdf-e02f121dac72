using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Shared.Application;

namespace Requests.Application.Flows.UpdateFlow;

public class UpdateFlowCommandHandler(IRequestsDbContext context)
    : IRequestHandler<UpdateFlowCommand, Result>
{
    public async Task<Result> Handle(UpdateFlowCommand request, CancellationToken cancellationToken)
    {
        var flow = await context.Flows
            .FirstOrDefaultAsync(f => f.Id == request.Id, cancellationToken);

        if (flow is null)
        {
            return Result.Failure("Flow bulunamadı");
        }

        var existingFlow = await context.Flows
            .FirstOrDefaultAsync(f => f.Name == request.Name && f.Id != request.Id, cancellationToken);

        if (existingFlow is not null)
        {
            return Result.Failure("Bu isimde başka bir flow zaten mevcut");
        }

        flow.Name = request.Name;
        flow.Description = request.Description ?? string.Empty;

        await context.SaveChangesAsync(cancellationToken);

        return Result.Success();
    }
}
