using FluentValidation;

namespace Requests.Application.Flows.UpdateFlow;

public class UpdateFlowCommandValidator : AbstractValidator<UpdateFlowCommand>
{
    public UpdateFlowCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage("Flow ID boş olamaz");

        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("Flow adı boş olamaz")
            .MaximumLength(100)
            .WithMessage("Flow adı maksimum 100 karakter olabilir");

        RuleFor(x => x.Description)
            .MaximumLength(500)
            .WithMessage("Açıklama maksimum 500 karakter olabilir");
    }
}
