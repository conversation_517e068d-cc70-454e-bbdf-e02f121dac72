namespace Requests.Application;

public class TransitionDto
{
    public Guid Id { get; set; }
    public Guid FromNodeId { get; set; }
    public Guid ToNodeId { get; set; }
    public string Name { get; set; } = string.Empty;
    public DateTime InsertDate { get; set; }
    public DateTime? UpdateDate { get; set; }
    public string FromNodeName { get; set; } = string.Empty;
    public string ToNodeName { get; set; } = string.Empty;
}

public class TransitionListItemDto
{
    public Guid Id { get; set; }
    public Guid FromNodeId { get; set; }
    public Guid ToNodeId { get; set; }
    public string Name { get; set; } = string.Empty;
    public DateTime InsertDate { get; set; }
    public DateTime? UpdateDate { get; set; }
    public string FromNodeName { get; set; } = string.Empty;
    public string ToNodeName { get; set; } = string.Empty;
}
