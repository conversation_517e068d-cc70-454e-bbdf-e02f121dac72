import { accountRouteList } from "@/apps/Account/Routes";
import { pausesRouteList } from "@/apps/Pauses/Routes";
import { callRouteList } from "@/apps/Call/Routes";
import { chatRouteList } from "@/apps/Chat/Routes";
import { dashboardRoutetList } from "@/apps/Dashboard/Routes";
import LayoutIndex from "@/apps/Layout/LayoutIndex";
import { settingsRouteList } from "@/apps/Settings/Routes";
import { teamRouteList } from "@/apps/Team/Routes";
import { Navigate, Route, Routes } from "react-router-dom";
import { notificationRouteList } from "@/apps/Notification/Routes";
import NotFound from "@/apps/Common/NotFound";
import Forbidden from "@/apps/Common/Forbidden";
import { commonRouteList } from "./CommontRoutes";

export const RouteList = () => {
  return (
    <>
      <Routes>
        <Route element={<LayoutIndex />}>
          {dashboardRoutetList}
          {commonRouteList}
          {pausesRouteList}
          {settingsRouteList}
          {chatRouteList}
          {teamRouteList}
          {callRouteList}
          {notificationRouteList}
        </Route>
        {accountRouteList}
        <Route path="/forbidden" element={<Forbidden />} />
        <Route path="/not-found" element={<NotFound />} />
        <Route path="*" element={<Navigate to={"/not-found"} />} />
      </Routes>
    </>
  );
};
