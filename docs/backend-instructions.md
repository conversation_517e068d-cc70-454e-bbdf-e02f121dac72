# Backend Geliştirme Talimatları - MasterCRM

## Teknoloji Yığını
- .NET 9 Web API
- SQL Server
- MediatR (CQRS için)
- FluentValidation
- Quartz.NET
- HybridCache
- In-Memory Service Bus

## Mi<PERSON><PERSON>

### 0. Backend Genel Talimatlar
- Use Primary Constructor in C# class
- Endpointlerde sadece Minimal API kullan. Ek paket kullanma.
-

### 1. Modüler Monolit Yapısı
- Her modül kendi içinde bağımsız olmalı
- Modüller arası iletişim iyi tanımlanmış arayüzler aracılığıyla yapılmalı
- Paylaşılan çekirdek minimum düzeyde olmalı
- Modül bağımlılığı gevşek olmalı

### 3. Geliştirme Standartları

#### Domain Katmanı
- Zengin Domain Modelleri kullanın
- Domain Olaylarını uygulayın
- Karmaşık özellikler için Value Object tanımlayın
- Result Pattern kullanın

```csharp
public class Entity : BaseEntity, IEntity
{
    public void BusinessMethod()
    {
        // <PERSON>ş mantığı
        AddDomainEvent(new BusinessEventOccurred());
    }
}
```

#### Application Katmanı
- Her dosyada bir Command/Query
- MediatR ile CQRS deseni kullan
- FluentValidation kullanarak validation yap
- namespace eklerken MasterCRM.Modules ile başlamasın
- command/query, validator, endpoint ve validator için ayrı ayrı dosyalar oluştur
- Endointler Command/Query yanında olmalı. (Vertical Slice Architecture)

#### Infrastructure Katmanı
- Harici servis entegrasyonları
- Veritabanı yapılandırmaları
- Modül spesifik altyapı servisleri

### 4. Veritabanı Yönergeleri
- Her modül kendi DbContext'ine sahip olmalı
- Entity Framework Core ile SQL Server kullanın
- Veritabanı adlandırma kurallarına uyun:
  - Tablolar: `[ModuleName].[EntityName]`
  - Sütunlar: PascalCase
  - Birincil Anahtarlar: `Id` (GUID)
  - Yabancı Anahtarlar: `EntityNameId`

### 5. API Endpoint Yapısı
```
/api/[module]/[resource]/[action]
```

Örnekler:
```
# Kullanıcı Modülü
GET    /api/v1/users/account/list
POST   /api/v1/users/account/create
GET    /api/v1/users/account/get/{id}
PUT    /api/v1/users/account/update/{id}
DELETE /api/v1/users/account/delete/{id}

# Müşteri Modülü
GET    /api/v1/customers/management/list
POST   /api/v1/customers/management/create
GET    /api/v1/customers/management/get/{id}
PUT    /api/v1/customers/management/update/{id}
DELETE /api/v1/customers/management/delete/{id}

# Görüşme Modülü
GET    /api/v1/conversations/calls/list
POST   /api/v1/conversations/calls/create
GET    /api/v1/conversations/calls/get/{id}
PUT    /api/v1/conversations/calls/update/{id}
DELETE /api/v1/conversations/calls/delete/{id}
```

### 6. Hata Yönetimi
- Result Pattern kullanımı
- HTTP API'ler için Problem Details implementasyonu
- Standart hata yanıt formatı:
```json
{
  "type": "https://tools.ietf.org/html/rfc7231#section-6.5.1",
  "title": "Hata başlığı",
  "status": 400,
  "detail": "Hata açıklaması",
  "isSuccess": "true"
}
```

### 7. Doğrulama
- Tüm command/query'ler için FluentValidation kullanımı
- MediatR pipeline'ında validation behavior implementasyonu
- Her command/query için ayrı doğrulama kuralları

### 8. Test Stratejisi
- Birim Testler: Domain mantığı ve Application servisleri
- Entegrasyon Testleri: Altyapı ve API endpoint'leri
- xUnit ve NSubstitute kullanımı
- AAA (Arrange-Act-Assert) pattern'i takibi

### 9. Performans Dikkatleri
- HybridCache kullanarak önbellekleme yapın
- async/await tutarlı bir şekilde kullanın
- Liste endpoint'leri için sayfalama uygulayın
- Okuma işlemleri için projeksiyonlar kullanın

### 10. Güvenlik Yönergeleri
- JWT kimlik doğrulaması uygulayın
- Rol tabanlı yetkilendirme
- Sadece HTTPS kullanın
- Denetim kaydı uygulayın
- Giriş doğrulama ve temizleme

### 11. İzleme ve Günlükleme
- Yapılandırılmış günlükleme için Serilog kullanın
- Sağlık kontrolleri uygulayın
- Performans metrikleri ekleyin
- Uygun günlükleme seviyelerini yapılandırın

### 12. Dağıtım
- Docker konteynerleri kullanın
- CI/CD için GitHub Actions yapılandırın
- Veritabanı geçişlerini uygulayın
- Ortam spesifik yapılandırmalar

### 13. Kod Organizasyonu
- Özellikleri odaklı ve küçük tutun
- SOLID prensiplerine uyun
- Bağımlılık enjeksiyonu kullanın
- Modüller arası iletişim için arabulucu deseni uygulayın

### 14. Dokümantasyon
- Genel API'ler için XML yorumları kullanın
- Swagger ile API dokümantasyonunu koruyun
- Modül bağımlılıklarını belgeleyin
- README dosyalarını güncel tutun

### 15. Geliştirme İş Akışı
1. Main dalından özellik dalı oluşturun
2. Yönergeleri takip ederek özelliği uygulayın
3. Testler yazın
4. Pull request oluşturun
5. Kod incelemesi
6. Main dalına birleştirin

### 16. Modül İletişimi
- Modüller arası iletişim için In-Memory Service Bus kullanın
- Uygun olduğunda olay odaklı mimari uygulayın
- Net modül sınırları tanımlayın
- Modüller arası iletişim için paylaşılan sözleşmeler kullanın
