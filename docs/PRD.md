# Product Requirements Document - MasterCRM

## Kısa Özet
MasterCRM, işletmelerin müşteri ilişkilerini etkin bir şekilde yönetmesini sağlayan, 3CX telefon entegrasyonlu, modüler bir CRM sistemidir. Dinamik form yapılar<PERSON>, gelişmiş iş akışı yönetimi ve çoklu iletişim kanalı desteği ile müşteri hizmetleri süreçlerini optimize eder.

## Motivasyon
- Müşteri hizmetleri süreçlerinin tek bir platform üzerinden yönetilmesi ihtiyacı
- Telefon görüşmelerinin otomatik kaydedilmesi ve müşteri bilgileriyle ilişkilendirilmesi
- İş akışlarının dinamik olarak yönetilebilmesi
- Çoklu iletişim kanallarının entegre edilmesi
- Müşteri verilerinin merkezi yönetimi

## Teknik Mimari

### Technology Stack
- Backend:
  - .NET 9 Web API
  - SQL Server
  - MediatR (CQRS implementasyonu için)
  - FluentValidation
  - Quartz.NET (Zamanlanmış görevler için)
  - HybridCache
  - In-Memory Service Bus

- Frontend:
  - React
  - Ant Design
  - Tailwind CSS

- DevOps:
  - Docker
  - GitHub Actions

- Architecture yaklaşımlar
  - Modular Monolith mimari
  - Vertical Slice Architecture
  - Result Pattern
  -

### System Architecture

```mermaid
graph TD
    Client[React Frontend] --> Host
    Host --> ModuleA[Users Module]
    Host --> ModuleB[Customers Module]
    Host --> ModuleC[Conversations Module]
    Host --> ModuleD[DynamicForms Module]
    Host --> ModuleE[Sales Module]
    Host --> ModuleF[Requests Module]
    Host --> ModuleG[Tasks Module]

    ModuleA --> DB[(SQL Server)]
    ModuleB --> DB
    ModuleC --> DB
    ModuleD --> DB
    ModuleE --> DB
    ModuleF --> DB
    ModuleG --> DB

    Host --> Cache[HybridCache]
    Host --> Queue[In-Memory ServiceBus]
    Host --> ThreeCX[3CX Integration]
```

### Project Structure
```
MasterCRM/
├── src/
│   ├── Modules/
│   │   ├── Users/
│   │   │   ├── Application/
│   │   │   │   ├── Account/
│   │   │   │   │   ├── CreateUser/
│   │   │   │   │   │   ├── CreateUserCommand.cs
│   │   │   │   │   │   ├── CreateUserCommandHandler.cs
│   │   │   │   │   │   ├── CreateUserCommandValidator.cs
│   │   │   │   │   │   └── CreateUserEndpoint.cs
│   │   │   │   │   └── GetUser/
│   │   │   │   │       ├── GetUserQuery.cs
│   │   │   │   │       ├── GetUserQueryHandler.cs
│   │   │   │   │       └── GetUserEndpoint.cs
│   │   │   │   ├── Manage/
│   │   │   │   ├── Auth/
│   │   │   └── Infrastructure/
│   │   │       ├── Data/
│   │   │       ├── External/
│   │   ├── Customers/
│   │   │   ├── Application/
│   │   │   └── Infrastructure/
│   │   ├── Conversations/
│   │   ├── DynamicForms/
│   │   ├── Sales/
│   │   ├── Requests/
│   │   └── Tasks/
│   ├── Shared/
│   │   ├── Application/
│   │   ├── Domain/
│   │   ├── Endpoints/
│   │   └── Infrastructure/
│   └── Host/
├── tests/
└── docs/
```

### Database Schema
```sql

-- Users Module
CREATE TABLE Users.User (
    Id UNIQUEIDENTIFIER PRIMARY KEY,InsertDate DATETIME2 NOT NULL,
    UpdateDate DATETIME2,
    InsertUserId UNIQUEIDENTIFIER,
    UpdateUserId UNIQUEIDENTIFIER,
    History NVARCHAR(MAX)
    Name NVARCHAR(100) NOT NULL,
    Surname NVARCHAR(100) NOT NULL,
    Email NVARCHAR(255) NOT NULL,
    Phone NVARCHAR(20),
    AttributeData NVARCHAR(MAX)
);

-- Diğer tablolar benzer şekilde devam eder...
```

### API Endpoints

#### Users Module
```
GET /api/v1/users/users
POST /api/v1/users/users
GET /api/v1/users/users/{id}
PUT /api/v1/users/users/{id}
DELETE /api/v1/users/users/{id}
```

[Diğer modüller için benzer endpoint yapıları]

### API Endpoints Organization
```
Modules/
├── ModuleName/
│   ├── Application/
│   │   ├── FeatureName/
│   │   │   ├── CreateFeature/
│   │   │   │   ├── CreateFeatureCommand.cs
│   │   │   │   ├── CreateFeatureCommandHandler.cs
│   │   │   │   ├── CreateFeatureCommandValidator.cs
│   │   │   │   └── CreateFeatureEndpoint.cs
│   │   │   └── GetFeature/
│   │   │       ├── GetFeatureQuery.cs
│   │   │       ├── GetFeatureQueryHandler.cs
│   │   │       └── GetFeatureEndpoint.cs
│   │   ├── FeatureName2/
│   │   ├── FeatureName3/
│   └── Infrastructure/
│       ├── Data/
│       ├── External/
```

### CQRS Implementation

Her modül için Feature klasörleri:

```
ModuleName/
   ├── CreateEntity/
   │   ├── CreateEntityCommand.cs
   │   └── CreateEntityCommandHandler.cs
   ├── UpdateEntity/
   ├── GetEntity/
   │   ├── GetEntityQuery.cs
   │   └── GetEntityQueryHandler.cs
   └── ListEntities/
```

## Alternatif Değerlendirmeleri
1. Microservices vs Modular Monolith
   - Modular Monolith seçildi çünkü:
     - Başlangıç maliyeti daha düşük
     - Deployment karmaşıklığı az
     - Modüller arası iletişim daha kolay

2. Entity Framework vs Dapper
   - Entity Framework seçildi çünkü:
     - Hızlı geliştirme imkanı
     - Change tracking özelliği
     - Migration yönetimi

## Uygulama Stratejileri
1. Aşamalı Geliştirme:
   - Faz 1: Temel Altyapı (2 hafta)
     - Modüler yapının kurulması
     - Shared kütüphanelerin hazırlanması
     - CI/CD pipeline kurulumu
     - Veritabanı migration altyapısı

   - Faz 2: Kullanıcı Yönetimi (3 hafta)
     - Kullanıcı işlemleri (CRUD)
     - Rol ve yetkilendirme sistemi
     - Kullanıcı oturum yönetimi
     - Audit logging altyapısı

   - Faz 3: Müşteri Yönetimi (4 hafta)
     - Müşteri kayıt sistemi
     - Müşteri iletişim bilgileri yönetimi
     - Dinamik form yapılarının oluşturulması
     - Müşteri danışman atama sistemi

   - Faz 4: İletişim Altyapısı (4 hafta)
     - 3CX entegrasyonu
     - Çağrı yönetim sistemi
     - Chat altyapısı
     - Mesajlaşma kanalları entegrasyonu

   - Faz 5: Ticket Sistemi (3 hafta)
     - Ticket oluşturma ve yönetimi
     - İş akışı (Flow) altyapısı
     - SLA takip sistemi
     - Bildirim sistemi

   - Faz 6: Görev Yönetimi (3 hafta)
     - Görev oluşturma ve atama
     - Görev iş akışları
     - Görev takip ve raporlama
     - Takvim entegrasyonu

   - Faz 7: Satış Yönetimi (3 hafta)
     - Ürün/Hizmet tanımlama
     - Sipariş yönetimi
     - Fiyatlandırma
     - Satış raporları

   - Faz 8: Raporlama ve Analytics (3 hafta)
     - Dashboard tasarımı
     - Performans raporları
     - Müşteri analitiği
     - İş süreç raporları

   - Faz 9: Optimizasyon ve Test (2 hafta)
     - Performans optimizasyonu
     - Güvenlik testleri
     - Load testing
     - UAT (User Acceptance Testing)

2. Test Stratejisi:
   - Unit Tests
   - Integration Tests
   - E2E Tests

## Ek Değerlendirmeler
- Performans optimizasyonu
- Veri yedekleme stratejisi
- Audit logging
- Multi-tenancy desteği
- GDPR uyumluluğu

## Başarı Metrikleri
- Sistem response time < 200ms
- Uptime > 99.9%
- Concurrent user support > 1000
- API call success rate > 99%

## Riskler ve Önlemler
1. Performans Riskleri:
   - Çözüm: Caching stratejisi, DB indexleme

2. Güvenlik Riskleri:
   - Çözüm: Regular security audits, penetration testing

3. Veri Tutarlılığı:
   - Çözüm: Transaction management, data validation

## Bağımlılıklar
- 3CX API entegrasyonu
- SQL Server
- Azure services (opsiyonel)
- SMTP server for notifications
