@baseUrl = https://localhost:7001
@token = {{auth_token}}

### List all 3CX Queues
GET {{baseUrl}}/api/v1/conversations/threecx-queues
Authorization: Bearer {{token}}

### Get specific 3CX Queue
GET {{baseUrl}}/api/v1/conversations/threecx-queues/100
Authorization: Bearer {{token}}

### Create new 3CX Queue
POST {{baseUrl}}/api/v1/conversations/threecx-queues
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "queueName": "Test Queue",
  "queueNumber": "200",
  "agents": ["Agent1 (101)", "Agent2 (102)"]
}

### Update 3CX Queue
PUT {{baseUrl}}/api/v1/conversations/threecx-queues/200
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "queueNumber": "200",
  "queueName": "Updated Test Queue",
  "agents": ["Agent1 (101)", "Agent2 (102)", "Agent3 (103)"]
}

### Delete 3CX Queue
DELETE {{baseUrl}}/api/v1/conversations/threecx-queues/200
Authorization: Bearer {{token}}

### List Queues (Legacy AutoDialers endpoint)
GET {{baseUrl}}/api/v1/conversations/autodialers/queues
Authorization: Bearer {{token}}
