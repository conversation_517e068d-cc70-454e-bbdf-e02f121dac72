### <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
@baseUrl = {{$dotenv domain}}
@email = {{$dotenv email}}
@password = {{$dotenv password}}

###

# @name login
POST {{baseUrl}}/api/v1/users/account/login
Content-Type: application/json

{
    "Email": "{{email}}",
    "Password": "{{password}}"
}

###

@token = {{login.response.body.AccessToken}}

### === ENTEGRE WORKFLOW TEST SENARYOSU ===
### Bu test, gerçek bir müşteri destek sürecini simüle eder

### 1. Ana Workflow Oluşturma
# @name createMainWorkflow
POST {{baseUrl}}/api/v1/requests/flows
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "Name": "Müşteri Destek Süreci",
    "Description": "Müşteri destek taleplerinin baştan sona işlenme süreci"
}

###

@mainFlowId = {{createMainWorkflow.response.body}}

### 2. Workflow Node'larını Oluşturma

### 2.1. Talep Alındı Node'u
# @name createRequestReceivedNode
POST {{baseUrl}}/api/v1/requests/nodes
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FlowId": "{{mainFlowId}}",
    "Name": "Talep Alındı",
    "Description": "Müşteri talebinin sisteme kaydedildiği ilk aşama",
    "Type": "Todo"
}

###

@requestReceivedNodeId = {{createRequestReceivedNode.response.body}}

### 2.2. Analiz Aşaması Node'u
# @name createAnalysisNode
POST {{baseUrl}}/api/v1/requests/nodes
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FlowId": "{{mainFlowId}}",
    "Name": "Analiz Aşaması",
    "Description": "Talebin teknik ekip tarafından analiz edildiği aşama",
    "Type": "Doing"
}

###

@analysisNodeId = {{createAnalysisNode.response.body}}

### 2.3. Geliştirme Aşaması Node'u
# @name createDevelopmentNode
POST {{baseUrl}}/api/v1/requests/nodes
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FlowId": "{{mainFlowId}}",
    "Name": "Geliştirme",
    "Description": "Çözümün geliştirildiği aşama",
    "Type": "Doing"
}

###

@developmentNodeId = {{createDevelopmentNode.response.body}}

### 2.4. Test Aşaması Node'u
# @name createTestingNode
POST {{baseUrl}}/api/v1/requests/nodes
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FlowId": "{{mainFlowId}}",
    "Name": "Test",
    "Description": "Çözümün test edildiği aşama",
    "Type": "Doing"
}

###

@testingNodeId = {{createTestingNode.response.body}}

### 2.5. Müşteri Onayı Node'u
# @name createCustomerApprovalNode
POST {{baseUrl}}/api/v1/requests/nodes
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FlowId": "{{mainFlowId}}",
    "Name": "Müşteri Onayı",
    "Description": "Çözümün müşteri tarafından onaylandığı aşama",
    "Type": "Doing"
}

###

@customerApprovalNodeId = {{createCustomerApprovalNode.response.body}}

### 2.6. Tamamlandı Node'u
# @name createCompletedNode
POST {{baseUrl}}/api/v1/requests/nodes
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FlowId": "{{mainFlowId}}",
    "Name": "Tamamlandı",
    "Description": "Talebin başarıyla tamamlandığı son aşama",
    "Type": "Done"
}

###

@completedNodeId = {{createCompletedNode.response.body}}

### 2.7. İptal Edildi Node'u
# @name createCancelledNode
POST {{baseUrl}}/api/v1/requests/nodes
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FlowId": "{{mainFlowId}}",
    "Name": "İptal Edildi",
    "Description": "Talebin iptal edildiği aşama",
    "Type": "Done"
}

###

@cancelledNodeId = {{createCancelledNode.response.body}}

### 3. Workflow Transition'larını Oluşturma

### 3.1. Talep Alındı -> Analiz
# @name createTransition1
POST {{baseUrl}}/api/v1/requests/transitions
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FromNodeId": "{{requestReceivedNodeId}}",
    "ToNodeId": "{{analysisNodeId}}",
    "Name": "Analize Gönder"
}

###

@transition1Id = {{createTransition1.response.body}}

### 3.2. Analiz -> Geliştirme
# @name createTransition2
POST {{baseUrl}}/api/v1/requests/transitions
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FromNodeId": "{{analysisNodeId}}",
    "ToNodeId": "{{developmentNodeId}}",
    "Name": "Geliştirmeye Gönder"
}

###

@transition2Id = {{createTransition2.response.body}}

### 3.3. Geliştirme -> Test
# @name createTransition3
POST {{baseUrl}}/api/v1/requests/transitions
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FromNodeId": "{{developmentNodeId}}",
    "ToNodeId": "{{testingNodeId}}",
    "Name": "Teste Gönder"
}

###

@transition3Id = {{createTransition3.response.body}}

### 3.4. Test -> Müşteri Onayı
# @name createTransition4
POST {{baseUrl}}/api/v1/requests/transitions
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FromNodeId": "{{testingNodeId}}",
    "ToNodeId": "{{customerApprovalNodeId}}",
    "Name": "Müşteri Onayına Gönder"
}

###

@transition4Id = {{createTransition4.response.body}}

### 3.5. Müşteri Onayı -> Tamamlandı
# @name createTransition5
POST {{baseUrl}}/api/v1/requests/transitions
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FromNodeId": "{{customerApprovalNodeId}}",
    "ToNodeId": "{{completedNodeId}}",
    "Name": "Tamamla"
}

###

@transition5Id = {{createTransition5.response.body}}

### 3.6. Analiz -> İptal (Alternatif yol)
# @name createTransition6
POST {{baseUrl}}/api/v1/requests/transitions
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FromNodeId": "{{analysisNodeId}}",
    "ToNodeId": "{{cancelledNodeId}}",
    "Name": "İptal Et"
}

###

@transition6Id = {{createTransition6.response.body}}

### 3.7. Test -> Geliştirme (Geri dönüş)
# @name createTransition7
POST {{baseUrl}}/api/v1/requests/transitions
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FromNodeId": "{{testingNodeId}}",
    "ToNodeId": "{{developmentNodeId}}",
    "Name": "Geliştirmeye Geri Gönder"
}

###

@transition7Id = {{createTransition7.response.body}}

### 3.8. Müşteri Onayı -> Geliştirme (Revizyon)
# @name createTransition8
POST {{baseUrl}}/api/v1/requests/transitions
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FromNodeId": "{{customerApprovalNodeId}}",
    "ToNodeId": "{{developmentNodeId}}",
    "Name": "Revizyon İçin Geri Gönder"
}

###

@transition8Id = {{createTransition8.response.body}}

### 4. Oluşturulan Workflow'u Kontrol Etme

### 4.1. Flow Detayını Görüntüle
GET {{baseUrl}}/api/v1/requests/flows/{{mainFlowId}}
Authorization: Bearer {{token}}

### 4.2. Tüm Node'ları Listele
GET {{baseUrl}}/api/v1/requests/nodes?FlowId={{mainFlowId}}&PageNumber=1&PageSize=20
Authorization: Bearer {{token}}

### 4.3. Tüm Transition'ları Listele
GET {{baseUrl}}/api/v1/requests/transitions?PageNumber=1&PageSize=20
Authorization: Bearer {{token}}

### 5. Workflow Kopyalama Testi

### 5.1. Workflow'u Kopyala
# @name copyWorkflow
POST {{baseUrl}}/api/v1/requests/flows/{{mainFlowId}}/copy
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "Name": "Müşteri Destek Süreci - Kopya",
    "Description": "Ana sürecin test kopyası"
}

###

@copiedFlowId = {{copyWorkflow.response.body}}

### 5.2. Kopyalanan Flow'un Node'larını Kontrol Et
GET {{baseUrl}}/api/v1/requests/nodes?FlowId={{copiedFlowId}}&PageNumber=1&PageSize=20
Authorization: Bearer {{token}}

### 6. Workflow Güncelleme Testleri

### 6.1. Bir Node'u Güncelle
PUT {{baseUrl}}/api/v1/requests/nodes/{{analysisNodeId}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FlowId": "{{mainFlowId}}",
    "Name": "Detaylı Analiz Aşaması",
    "Description": "Talebin teknik ekip tarafından detaylı olarak analiz edildiği güncellenmiş aşama",
    "Type": "Doing"
}

### 6.2. Bir Transition'u Güncelle
PUT {{baseUrl}}/api/v1/requests/transitions/{{transition1Id}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "Name": "Detaylı Analize Gönder"
}

### 7. Temizlik İşlemleri

### 7.1. Kopyalanan Flow'u Sil
DELETE {{baseUrl}}/api/v1/requests/flows/{{copiedFlowId}}
Authorization: Bearer {{token}}

### 7.2. Ana Flow'u Sil (Cascade silme ile tüm node ve transition'lar silinecek)
DELETE {{baseUrl}}/api/v1/requests/flows/{{mainFlowId}}
Authorization: Bearer {{token}}

### 8. Temizlik Sonrası Kontrol

### 8.1. Flow'ların silindiğini kontrol et
GET {{baseUrl}}/api/v1/requests/flows?PageNumber=1&PageSize=10
Authorization: Bearer {{token}}

### 8.2. Node'ların silindiğini kontrol et
GET {{baseUrl}}/api/v1/requests/nodes?PageNumber=1&PageSize=10
Authorization: Bearer {{token}}

### 8.3. Transition'ların silindiğini kontrol et
GET {{baseUrl}}/api/v1/requests/transitions?PageNumber=1&PageSize=10
Authorization: Bearer {{token}}
