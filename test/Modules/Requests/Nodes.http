### <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
@baseUrl = {{$dotenv domain}}
@email = {{$dotenv email}}
@password = {{$dotenv password}}

###

# @name login
POST {{baseUrl}}/api/v1/users/account/login
Content-Type: application/json

{
    "Email": "{{email}}",
    "Password": "{{password}}"
}

###

@token = {{login.response.body.AccessToken}}

### Test Flow Oluşturma
# @name createTestFlow
POST {{baseUrl}}/api/v1/requests/flows
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "Name": "Node Test Workflow",
    "Description": "Node testleri için workflow"
}

###

@flowId = {{createTestFlow.response.body.Value}}

### Node Oluşturma - Todo
# @name createTodoNode
POST {{baseUrl}}/api/v1/requests/nodes
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FlowId": "{{flowId}}",
    "Name": "Tale<PERSON> Alındı",
    "Description": "Müşteri talebinin alındığı ilk aşama",
    "NodeType": 1
}

###

@todoNodeId = {{createTodoNode.response.body.Value}}

### Node Oluşturma - Doing
# @name createDoingNode
POST {{baseUrl}}/api/v1/requests/nodes
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FlowId": "{{flowId}}",
    "Name": "İnceleme Aşaması",
    "Description": "Talebin teknik ekip tarafından incelendiği aşama",
    "NodeType": 2
}

###

@doingNodeId = {{createDoingNode.response.body.Value}}

### Node Oluşturma - Done
# @name createDoneNode
POST {{baseUrl}}/api/v1/requests/nodes
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FlowId": "{{flowId}}",
    "Name": "Tamamlandı",
    "Description": "Talebin başarıyla tamamlandığı son aşama",
    "NodeType": 3
}

###

@doneNodeId = {{createDoneNode.response.body.Value}}

### Node Listesi - Flow'a göre
GET {{baseUrl}}/api/v1/requests/nodes?FlowId={{flowId}}&PageNumber=1&PageSize=10
Authorization: Bearer {{token}}

### Node Listesi - Arama ile
GET {{baseUrl}}/api/v1/requests/nodes?SearchTerm=Talep&PageNumber=1&PageSize=10
Authorization: Bearer {{token}}

### Node Listesi - Type'a göre
GET {{baseUrl}}/api/v1/requests/nodes?Type=Todo&PageNumber=1&PageSize=10
Authorization: Bearer {{token}}

### Node Detayı
GET {{baseUrl}}/api/v1/requests/nodes/{{todoNodeId}}
Authorization: Bearer {{token}}

### Node Güncelleme
PUT {{baseUrl}}/api/v1/requests/nodes/{{todoNodeId}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FlowId": "{{flowId}}",
    "Name": "Talep Alındı ve Kaydedildi",
    "Description": "Müşteri talebinin alınıp sisteme kaydedildiği güncellenmiş aşama",
    "Type": "Todo"
}

### İkinci Test Flow Oluşturma
# @name createSecondFlow
POST {{baseUrl}}/api/v1/requests/flows
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "Name": "İkinci Test Workflow",
    "Description": "Node benzersizlik testi için ikinci workflow"
}

###

@secondFlowId = {{createSecondFlow.response.body}}

### Aynı isimde Node oluşturma - Farklı Flow'da (başarılı olmalı)
# @name createSameNameDifferentFlow
POST {{baseUrl}}/api/v1/requests/nodes
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FlowId": "{{secondFlowId}}",
    "Name": "Talep Alındı",
    "Description": "İkinci flow'da aynı isimli node",
    "Type": "Todo"
}

###

@sameNameNodeId = {{createSameNameDifferentFlow.response.body}}

### Hata Durumu Testleri

### Olmayan Flow için Node oluşturma
POST {{baseUrl}}/api/v1/requests/nodes
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FlowId": "00000000-0000-0000-0000-000000000000",
    "Name": "Test Node",
    "Description": "Olmayan flow için test",
    "Type": "Todo"
}

### Aynı Flow'da aynı isimde Node oluşturma (hata vermeli)
POST {{baseUrl}}/api/v1/requests/nodes
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FlowId": "{{flowId}}",
    "Name": "Talep Alındı ve Kaydedildi",
    "Description": "Aynı isimde node testi",
    "Type": "Todo"
}

### Geçersiz veri ile Node oluşturma (boş name)
POST {{baseUrl}}/api/v1/requests/nodes
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FlowId": "{{flowId}}",
    "Name": "",
    "Description": "Boş isim testi",
    "Type": "Todo"
}

### Geçersiz NodeType ile Node oluşturma
POST {{baseUrl}}/api/v1/requests/nodes
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FlowId": "{{flowId}}",
    "Name": "Geçersiz Type Node",
    "Description": "Geçersiz type testi",
    "Type": "InvalidType"
}

### Olmayan Node detayı
GET {{baseUrl}}/api/v1/requests/nodes/00000000-0000-0000-0000-000000000000
Authorization: Bearer {{token}}

### Olmayan Node güncelleme
PUT {{baseUrl}}/api/v1/requests/nodes/00000000-0000-0000-0000-000000000000
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "FlowId": "{{flowId}}",
    "Name": "Olmayan Node",
    "Description": "Bu node mevcut değil",
    "Type": "Todo"
}

### Node Silme - İkinci flow'daki node
DELETE {{baseUrl}}/api/v1/requests/nodes/{{sameNameNodeId}}
Authorization: Bearer {{token}}

### Node Silme - Done node
DELETE {{baseUrl}}/api/v1/requests/nodes/{{doneNodeId}}
Authorization: Bearer {{token}}

### Node Silme - Doing node
DELETE {{baseUrl}}/api/v1/requests/nodes/{{doingNodeId}}
Authorization: Bearer {{token}}

### Node Silme - Todo node
DELETE {{baseUrl}}/api/v1/requests/nodes/{{todoNodeId}}
Authorization: Bearer {{token}}

### Olmayan Node silme
DELETE {{baseUrl}}/api/v1/requests/nodes/00000000-0000-0000-0000-000000000000
Authorization: Bearer {{token}}

### Test Flow'ları Temizleme
DELETE {{baseUrl}}/api/v1/requests/flows/{{secondFlowId}}
Authorization: Bearer {{token}}

###

DELETE {{baseUrl}}/api/v1/requests/flows/{{flowId}}
Authorization: Bearer {{token}}

### Son Node Listesi - Silme işlemlerini kontrol et
GET {{baseUrl}}/api/v1/requests/nodes?PageNumber=1&PageSize=10
Authorization: Bearer {{token}}
