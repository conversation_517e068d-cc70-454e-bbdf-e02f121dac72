### <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
@baseUrl = {{$dotenv domain}}
@email = {{$dotenv email}}
@password = {{$dotenv password}}

###

# @name login
POST {{baseUrl}}/api/v1/users/account/login
Content-Type: application/json

{
    "Email": "{{email}}",
    "Password": "{{password}}"
}

###

@token = {{login.response.body.AccessToken}}

### Flow Oluşturma - Temel
# @name createFlow
POST {{baseUrl}}/api/v1/requests/flows
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "Name": "Test Workflow",
    "Description": "Test amaçlı workflow"
}

###

@flowId = {{createFlow.response.body.Value}}

### Flow Oluşturma - Detaylı
# @name createDetailedFlow
POST {{baseUrl}}/api/v1/requests/flows
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "Name": "Müşteri Destek Süreci",
    "Description": "Müşteri destek taleplerinin işlenme süreci. Bu süreç talep alımından çözüme kadar tüm adımları içerir."
}

###

@detailedFlowId = {{createDetailedFlow.response.body.Value}}

### Flow Listesi - Sayfalama ile
GET {{baseUrl}}/api/v1/requests/flows?PageNumber=1&PageSize=10
Authorization: Bearer {{token}}

### Flow Listesi - Arama ile
GET {{baseUrl}}/api/v1/requests/flows?SearchTerm=Test&PageNumber=1&PageSize=10
Authorization: Bearer {{token}}

### Flow Detayı
GET {{baseUrl}}/api/v1/requests/flows/{{flowId}}
Authorization: Bearer {{token}}

### Flow Güncelleme
PUT {{baseUrl}}/api/v1/requests/flows/{{flowId}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "Name": "Güncellenmiş Test Workflow",
    "Description": "Güncellenmiş açıklama ile test workflow"
}

### Flow Kopyalama
# @name copyFlow
POST {{baseUrl}}/api/v1/requests/flows/{{detailedFlowId}}/copy
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "NewName": "Kopyalanmış Müşteri Destek Süreci",
    "NewDescription": "Orijinal sürecin kopyası"
}

###

@copiedFlowId = {{copyFlow.response.body.Value}}

### Kopyalanan Flow Detayı
GET {{baseUrl}}/api/v1/requests/flows/{{copiedFlowId}}
Authorization: Bearer {{token}}

### Hata Durumu Testleri

### Olmayan Flow Detayı
GET {{baseUrl}}/api/v1/requests/flows/00000000-0000-0000-0000-000000000000
Authorization: Bearer {{token}}

### Geçersiz veri ile Flow oluşturma (boş name)
POST {{baseUrl}}/api/v1/requests/flows
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "Name": "",
    "Description": "Boş isim ile test"
}

### Çok uzun isim ile Flow oluşturma
POST {{baseUrl}}/api/v1/requests/flows
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "Name": "Bu çok uzun bir flow ismi Bu çok uzun bir flow ismi Bu çok uzun bir flow ismi Bu çok uzun bir flow ismi Bu çok uzun bir flow ismi Bu çok uzun bir flow ismi Bu çok uzun bir flow ismi Bu çok uzun bir flow ismi Bu çok uzun bir flow ismi Bu çok uzun bir flow ismi",
    "Description": "Çok uzun isim testi"
}

### Olmayan Flow güncelleme
PUT {{baseUrl}}/api/v1/requests/flows/00000000-0000-0000-0000-000000000001
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "Name": "Olmayan Flow",
    "Description": "Bu flow mevcut değil"
}

### Olmayan Flow kopyalama
POST {{baseUrl}}/api/v1/requests/flows/00000000-0000-0000-0000-000000000001/copy
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "NewName": "Olmayan Flow Kopyası",
    "NewDescription": "Bu flow mevcut değil"
}

### Flow Silme - Kopyalanan flow
DELETE {{baseUrl}}/api/v1/requests/flows/{{copiedFlowId}}
Authorization: Bearer {{token}}

### Flow Silme - Detaylı flow
DELETE {{baseUrl}}/api/v1/requests/flows/{{detailedFlowId}}
Authorization: Bearer {{token}}

### Flow Silme - Ana flow
DELETE {{baseUrl}}/api/v1/requests/flows/{{flowId}}
Authorization: Bearer {{token}}

### Olmayan Flow silme
DELETE {{baseUrl}}/api/v1/requests/flows/00000000-0000-0000-0000-000000000000
Authorization: Bearer {{token}}

### Son Flow Listesi - Silme işlemlerini kontrol et
GET {{baseUrl}}/api/v1/requests/flows?PageNumber=1&PageSize=10
Authorization: Bearer {{token}}
